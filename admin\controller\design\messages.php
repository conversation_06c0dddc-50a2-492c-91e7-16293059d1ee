<?php
class ControllerDesignMessages extends Controller {
	private $error = array();

	public function index() {
		$this->load->language('design/messages');

		$this->document->setTitle($this->language->get('heading_title'));

		$this->load->model('design/messages');

		$this->getList();
	}

	public function add() {
		$this->load->language('design/messages');

		$this->document->setTitle($this->language->get('heading_title'));

		$this->load->model('design/messages');

		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
			$this->model_design_messages->addMessage($this->request->post);

			$this->session->data['success'] = $this->language->get('text_success');

			$url = $this->buildUrlParams();

			$this->response->redirect($this->url->link('design/messages', 'token=' . $this->session->data['token'] . $url, true));
		}

		$this->getForm();
	}

	public function edit() {
		$this->load->language('design/messages');

		$this->document->setTitle($this->language->get('heading_title'));

		$this->load->model('design/messages');

		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
			$this->model_design_messages->editMessage($this->request->get['message_id'], $this->request->post);

			$this->session->data['success'] = $this->language->get('text_success');

			$url = $this->buildUrlParams();

			$this->response->redirect($this->url->link('design/messages', 'token=' . $this->session->data['token'] . $url, true));
		}

		$this->getForm();
	}

	public function delete() {
		$this->load->language('design/messages');

		$this->document->setTitle($this->language->get('heading_title'));

		$this->load->model('design/messages');

		if (isset($this->request->post['selected']) && $this->validateDelete()) {
			foreach ($this->request->post['selected'] as $message_id) {
				$this->model_design_messages->deleteMessage($message_id);
			}

			$this->session->data['success'] = $this->language->get('text_success');

			$url = $this->buildUrlParams();

			$this->response->redirect($this->url->link('design/messages', 'token=' . $this->session->data['token'] . $url, true));
		}

		$this->getList();
	}

	public function bulkStatus() {
		$this->load->language('design/messages');
		$this->load->model('design/messages');

		$json = array();

		if (isset($this->request->post['selected']) && $this->validateDelete()) {
			$status = isset($this->request->post['status']) ? (int)$this->request->post['status'] : 0;
			
			foreach ($this->request->post['selected'] as $message_id) {
				$this->model_design_messages->updateMessageStatus($message_id, $status);
			}

			$json['success'] = $this->language->get('text_success');
		} else {
			$json['error'] = $this->language->get('error_permission');
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}

	private function buildUrlParams() {
		$url = '';

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		return $url;
	}

	protected function getList() {
		$sort = isset($this->request->get['sort']) ? $this->request->get['sort'] : 'name';
		$order = isset($this->request->get['order']) ? $this->request->get['order'] : 'ASC';
		$page = isset($this->request->get['page']) ? $this->request->get['page'] : 1;

		$url = $this->buildUrlParams();

		$data['breadcrumbs'] = $this->buildBreadcrumbs($url);
		$data['add'] = $this->url->link('design/messages/add', 'token=' . $this->session->data['token'] . $url, true);
		$data['delete'] = $this->url->link('design/messages/delete', 'token=' . $this->session->data['token'] . $url, true);
		$data['bulk_status'] = $this->url->link('design/messages/bulkStatus', 'token=' . $this->session->data['token'] . $url, true);

		$data['messages'] = $this->getMessagesData($sort, $order, $page, $url);
		$data['pagination'] = $this->buildPagination($sort, $order, $page, $url);

		$this->setListLanguageData($data);
		$this->setSortingData($data, $sort, $order, $url);
		$this->setCommonData($data);

		$this->response->setOutput($this->load->view('design/messages_list', $data));
	}

	private function buildBreadcrumbs($url) {
		$breadcrumbs = array();

		$breadcrumbs[] = array(
			'text' => $this->language->get('text_home'),
			'href' => $this->url->link('common/dashboard', 'token=' . $this->session->data['token'], true)
		);

		$breadcrumbs[] = array(
			'text' => $this->language->get('heading_title'),
			'href' => $this->url->link('design/messages', 'token=' . $this->session->data['token'] . $url, true)
		);

		return $breadcrumbs;
	}

	private function getMessagesData($sort, $order, $page, $url) {
		$filter_data = array(
			'sort'  => $sort,
			'order' => $order,
			'start' => ($page - 1) * $this->config->get('config_limit_admin'),
			'limit' => $this->config->get('config_limit_admin')
		);

		$results = $this->model_design_messages->getMessages($filter_data);
		$messages = array();

		foreach ($results as $result) {
			$messages[] = array(
				'message_id'    => $result['message_id'],
				'name'          => $result['name'],
				'display_place' => $this->getDisplayPlaceName($result['display_place']),
				'start_date'    => date('d.m.Y H:i', strtotime($result['start_date'])),
				'end_date'      => $result['end_date'] ? date('d.m.Y H:i', strtotime($result['end_date'])) : $this->language->get('text_no_end_date'),
				'status'        => ($result['status'] ? $this->language->get('text_enabled') : $this->language->get('text_disabled')),
				'edit'          => $this->url->link('design/messages/edit', 'token=' . $this->session->data['token'] . '&message_id=' . $result['message_id'] . $url, true)
			);
		}

		return $messages;
	}

	private function getDisplayPlaceName($place_key) {
		$places = $this->getDisplayPlaces();
		return isset($places[$place_key]) ? $places[$place_key] : $place_key;
	}

	private function getDisplayPlaces() {
		return array(
			'index_page' => $this->language->get('text_display_place_index_page')
		);
	}

	private function buildPagination($sort, $order, $page, $url) {
		$message_total = $this->model_design_messages->getTotalMessages();

		$pagination = new Pagination();
		$pagination->total = $message_total;
		$pagination->page = $page;
		$pagination->limit = $this->config->get('config_limit_admin');
		$pagination->url = $this->url->link('design/messages', 'token=' . $this->session->data['token'] . $url . '&page={page}', true);

		return $pagination->render();
	}

	private function setListLanguageData(&$data) {
		$data['heading_title'] = $this->language->get('heading_title');
		$data['text_list'] = $this->language->get('text_list');
		$data['text_no_results'] = $this->language->get('text_no_results');
		$data['text_confirm'] = $this->language->get('text_confirm');

		$data['column_name'] = $this->language->get('column_name');
		$data['column_display_place'] = $this->language->get('column_display_place');
		$data['column_start_date'] = $this->language->get('column_start_date');
		$data['column_end_date'] = $this->language->get('column_end_date');
		$data['column_status'] = $this->language->get('column_status');
		$data['column_action'] = $this->language->get('column_action');

		$data['button_add'] = $this->language->get('button_add');
		$data['button_edit'] = $this->language->get('button_edit');
		$data['button_delete'] = $this->language->get('button_delete');
		$data['button_enable'] = $this->language->get('button_enable');
		$data['button_disable'] = $this->language->get('button_disable');
	}

	private function setSortingData(&$data, $sort, $order, $url) {
		$sort_url = '';

		if ($order == 'ASC') {
			$sort_url .= '&order=DESC';
		} else {
			$sort_url .= '&order=ASC';
		}

		if (isset($this->request->get['page'])) {
			$sort_url .= '&page=' . $this->request->get['page'];
		}

		$data['sort_name'] = $this->url->link('design/messages', 'token=' . $this->session->data['token'] . '&sort=name' . $sort_url, true);
		$data['sort_display_place'] = $this->url->link('design/messages', 'token=' . $this->session->data['token'] . '&sort=display_place' . $sort_url, true);
		$data['sort_start_date'] = $this->url->link('design/messages', 'token=' . $this->session->data['token'] . '&sort=start_date' . $sort_url, true);
		$data['sort_status'] = $this->url->link('design/messages', 'token=' . $this->session->data['token'] . '&sort=status' . $sort_url, true);

		$data['sort'] = $sort;
		$data['order'] = $order;
	}

	private function setCommonData(&$data) {
		if (isset($this->error['warning'])) {
			$data['error_warning'] = $this->error['warning'];
		} else {
			$data['error_warning'] = '';
		}

		if (isset($this->session->data['success'])) {
			$data['success'] = $this->session->data['success'];
			unset($this->session->data['success']);
		} else {
			$data['success'] = '';
		}

		if (isset($this->request->post['selected'])) {
			$data['selected'] = (array)$this->request->post['selected'];
		} else {
			$data['selected'] = array();
		}

		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');
	}

	protected function getForm() {
		$data['heading_title'] = $this->language->get('heading_title');

		$data['text_form'] = !isset($this->request->get['message_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');
		$data['text_enabled'] = $this->language->get('text_enabled');
		$data['text_disabled'] = $this->language->get('text_disabled');

		$this->setFormLanguageData($data);
		$this->setFormErrorData($data);
		$this->setFormUrls($data);
		$this->setFormFieldData($data);

		$data['display_places'] = $this->getDisplayPlaces();
		$data['token'] = $this->session->data['token'];

		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');

		$this->response->setOutput($this->load->view('design/messages_form', $data));
	}

	private function setFormLanguageData(&$data) {
		$data['entry_name'] = $this->language->get('entry_name');
		$data['entry_text'] = $this->language->get('entry_text');
		$data['entry_start_date'] = $this->language->get('entry_start_date');
		$data['entry_end_date'] = $this->language->get('entry_end_date');
		$data['entry_display_place'] = $this->language->get('entry_display_place');
		$data['entry_status'] = $this->language->get('entry_status');

		$data['button_save'] = $this->language->get('button_save');
		$data['button_cancel'] = $this->language->get('button_cancel');

		$data['help_end_date'] = $this->language->get('help_end_date');
		$data['help_display_place'] = $this->language->get('help_display_place');
	}

	private function setFormErrorData(&$data) {
		$error_fields = ['warning', 'name', 'text', 'start_date', 'end_date', 'display_place'];

		foreach ($error_fields as $field) {
			if (isset($this->error[$field])) {
				$data['error_' . $field] = $this->error[$field];
			} else {
				$data['error_' . $field] = '';
			}
		}
	}

	private function setFormUrls(&$data) {
		$url = $this->buildUrlParams();

		$data['breadcrumbs'] = $this->buildBreadcrumbs($url);

		if (!isset($this->request->get['message_id'])) {
			$data['action'] = $this->url->link('design/messages/add', 'token=' . $this->session->data['token'] . $url, true);
		} else {
			$data['action'] = $this->url->link('design/messages/edit', 'token=' . $this->session->data['token'] . '&message_id=' . $this->request->get['message_id'] . $url, true);
		}

		$data['cancel'] = $this->url->link('design/messages', 'token=' . $this->session->data['token'] . $url, true);
	}

	private function setFormFieldData(&$data) {
		if (isset($this->request->get['message_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
			$message_info = $this->model_design_messages->getMessage($this->request->get['message_id']);
		}

		$fields = ['name', 'text', 'start_date', 'end_date', 'display_place', 'status'];

		foreach ($fields as $field) {
			if (isset($this->request->post[$field])) {
				$data[$field] = $this->request->post[$field];
			} elseif (!empty($message_info)) {
				$data[$field] = $message_info[$field];
			} else {
				$data[$field] = ($field == 'status') ? 1 : '';
			}
		}

		// Форматиране на датите за показване
		if (!empty($data['start_date']) && !isset($this->request->post['start_date'])) {
			$data['start_date'] = date('Y-m-d H:i', strtotime($data['start_date']));
		}

		if (!empty($data['end_date']) && !isset($this->request->post['end_date'])) {
			$data['end_date'] = date('Y-m-d H:i', strtotime($data['end_date']));
		}
	}

	protected function validateForm() {
		if (!$this->user->hasPermission('modify', 'design/messages')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		if ((utf8_strlen($this->request->post['name']) < 3) || (utf8_strlen($this->request->post['name']) > 255)) {
			$this->error['name'] = $this->language->get('error_name');
		}

		if (utf8_strlen($this->request->post['text']) < 1) {
			$this->error['text'] = $this->language->get('error_text');
		}

		if (empty($this->request->post['start_date'])) {
			$this->error['start_date'] = $this->language->get('error_start_date');
		} elseif (!$this->validateDateTime($this->request->post['start_date'])) {
			$this->error['start_date'] = $this->language->get('error_start_date_format');
		}

		if (!empty($this->request->post['end_date'])) {
			if (!$this->validateDateTime($this->request->post['end_date'])) {
				$this->error['end_date'] = $this->language->get('error_end_date_format');
			} elseif (!empty($this->request->post['start_date']) &&
					  strtotime($this->request->post['end_date']) <= strtotime($this->request->post['start_date'])) {
				$this->error['end_date'] = $this->language->get('error_end_date_before_start');
			}
		}

		if (empty($this->request->post['display_place'])) {
			$this->error['display_place'] = $this->language->get('error_display_place');
		}

		return !$this->error;
	}

	private function validateDateTime($datetime) {
		$d = DateTime::createFromFormat('Y-m-d H:i', $datetime);
		return $d && $d->format('Y-m-d H:i') === $datetime;
	}

	protected function validateDelete() {
		if (!$this->user->hasPermission('modify', 'design/messages')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		return !$this->error;
	}
}
