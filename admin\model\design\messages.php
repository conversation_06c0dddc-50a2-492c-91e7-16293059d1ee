<?php
class ModelDesignMessages extends Model {
	
	public function addMessage($data) {
		$sql = "INSERT INTO `" . DB_PREFIX . "messages` SET 
				`name` = '" . $this->db->escape($data['name']) . "', 
				`text` = '" . $this->db->escape($data['text']) . "', 
				`start_date` = '" . $this->db->escape($data['start_date']) . "', 
				`end_date` = " . (!empty($data['end_date']) ? "'" . $this->db->escape($data['end_date']) . "'" : "NULL") . ", 
				`display_place` = '" . $this->db->escape($data['display_place']) . "', 
				`status` = '" . (int)$data['status'] . "', 
				`date_created` = NOW(), 
				`date_modified` = NOW()";

		$this->db->query($sql);

		return $this->db->getLastId();
	}

	public function editMessage($message_id, $data) {
		$sql = "UPDATE `" . DB_PREFIX . "messages` SET 
				`name` = '" . $this->db->escape($data['name']) . "', 
				`text` = '" . $this->db->escape($data['text']) . "', 
				`start_date` = '" . $this->db->escape($data['start_date']) . "', 
				`end_date` = " . (!empty($data['end_date']) ? "'" . $this->db->escape($data['end_date']) . "'" : "NULL") . ", 
				`display_place` = '" . $this->db->escape($data['display_place']) . "', 
				`status` = '" . (int)$data['status'] . "', 
				`date_modified` = NOW() 
				WHERE `message_id` = '" . (int)$message_id . "'";

		$this->db->query($sql);
	}

	public function deleteMessage($message_id) {
		$this->db->query("DELETE FROM `" . DB_PREFIX . "messages` WHERE `message_id` = '" . (int)$message_id . "'");
	}

	public function getMessage($message_id) {
		$query = $this->db->query("SELECT * FROM `" . DB_PREFIX . "messages` WHERE `message_id` = '" . (int)$message_id . "'");

		return $query->row;
	}

	public function getMessages($data = array()) {
		$sql = "SELECT * FROM `" . DB_PREFIX . "messages`";

		$sort_data = array(
			'name',
			'display_place',
			'start_date',
			'end_date',
			'status',
			'date_created',
			'date_modified'
		);

		if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
			$sql .= " ORDER BY `" . $data['sort'] . "`";
		} else {
			$sql .= " ORDER BY `name`";
		}

		if (isset($data['order']) && ($data['order'] == 'DESC')) {
			$sql .= " DESC";
		} else {
			$sql .= " ASC";
		}

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalMessages() {
		$query = $this->db->query("SELECT COUNT(*) AS total FROM `" . DB_PREFIX . "messages`");

		return $query->row['total'];
	}

	public function updateMessageStatus($message_id, $status) {
		$sql = "UPDATE `" . DB_PREFIX . "messages` SET 
				`status` = '" . (int)$status . "', 
				`date_modified` = NOW() 
				WHERE `message_id` = '" . (int)$message_id . "'";

		$this->db->query($sql);
	}

	public function getActiveMessages($display_place = null) {
		$sql = "SELECT * FROM `" . DB_PREFIX . "messages` 
				WHERE `status` = '1' 
				AND `start_date` <= NOW() 
				AND (`end_date` IS NULL OR `end_date` >= NOW())";

		if ($display_place) {
			$sql .= " AND `display_place` = '" . $this->db->escape($display_place) . "'";
		}

		$sql .= " ORDER BY `start_date` ASC";

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getMessagesByPlace($display_place) {
		return $this->getActiveMessages($display_place);
	}

	public function getExpiredMessages() {
		$sql = "SELECT * FROM `" . DB_PREFIX . "messages` 
				WHERE `status` = '1' 
				AND `end_date` IS NOT NULL 
				AND `end_date` < NOW() 
				ORDER BY `end_date` DESC";

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getUpcomingMessages() {
		$sql = "SELECT * FROM `" . DB_PREFIX . "messages` 
				WHERE `status` = '1' 
				AND `start_date` > NOW() 
				ORDER BY `start_date` ASC";

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getMessagesStatistics() {
		$stats = array();

		// Общо съобщения
		$query = $this->db->query("SELECT COUNT(*) AS total FROM `" . DB_PREFIX . "messages`");
		$stats['total'] = $query->row['total'];

		// Активни съобщения
		$query = $this->db->query("SELECT COUNT(*) AS total FROM `" . DB_PREFIX . "messages` WHERE `status` = '1'");
		$stats['active'] = $query->row['total'];

		// Текущо показвани съобщения
		$query = $this->db->query("SELECT COUNT(*) AS total FROM `" . DB_PREFIX . "messages` 
								   WHERE `status` = '1' 
								   AND `start_date` <= NOW() 
								   AND (`end_date` IS NULL OR `end_date` >= NOW())");
		$stats['current'] = $query->row['total'];

		// Изтекли съобщения
		$query = $this->db->query("SELECT COUNT(*) AS total FROM `" . DB_PREFIX . "messages` 
								   WHERE `status` = '1' 
								   AND `end_date` IS NOT NULL 
								   AND `end_date` < NOW()");
		$stats['expired'] = $query->row['total'];

		// Предстоящи съобщения
		$query = $this->db->query("SELECT COUNT(*) AS total FROM `" . DB_PREFIX . "messages` 
								   WHERE `status` = '1' 
								   AND `start_date` > NOW()");
		$stats['upcoming'] = $query->row['total'];

		return $stats;
	}

	public function installTable() {
		$sql = "CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "messages` (
			`message_id` int(11) NOT NULL AUTO_INCREMENT,
			`name` varchar(255) NOT NULL,
			`text` text NOT NULL,
			`start_date` datetime NOT NULL,
			`end_date` datetime DEFAULT NULL,
			`display_place` varchar(100) NOT NULL,
			`status` tinyint(1) NOT NULL DEFAULT '1',
			`date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
			`date_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			PRIMARY KEY (`message_id`),
			KEY `idx_status` (`status`),
			KEY `idx_display_place` (`display_place`),
			KEY `idx_dates` (`start_date`, `end_date`),
			KEY `idx_active` (`status`, `start_date`, `end_date`)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;";

		$this->db->query($sql);
	}

	public function uninstallTable() {
		$this->db->query("DROP TABLE IF EXISTS `" . DB_PREFIX . "messages`");
	}
}
