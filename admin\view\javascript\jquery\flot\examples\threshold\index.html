<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<title>Flot Examples: Thresholds</title>
	<link href="../examples.css" rel="stylesheet" type="text/css">
	<!--[if lte IE 8]><script language="javascript" type="text/javascript" src="../../excanvas.min.js"></script><![endif]-->
	<script language="javascript" type="text/javascript" src="../../jquery.js"></script>
	<script language="javascript" type="text/javascript" src="../../jquery.flot.js"></script>
	<script language="javascript" type="text/javascript" src="../../jquery.flot.threshold.js"></script>
	<script type="text/javascript">

	$(function() {

		var d1 = [];
		for (var i = 0; i <= 60; i += 1) {
			d1.push([i, parseInt(Math.random() * 30 - 10)]);
		}

		function plotWithOptions(t) {
			$.plot("#placeholder", [{
				data: d1,
				color: "rgb(30, 180, 20)",
				threshold: {
					below: t,
					color: "rgb(200, 20, 30)"
				},
				lines: {
					steps: true
				}
			}]);
		}

		plotWithOptions(0);

		$(".controls button").click(function (e) {
			e.preventDefault();
			var t = parseFloat($(this).text().replace("Threshold at ", ""));
			plotWithOptions(t);
		});

		// Add the Flot version string to the footer

		$("#footer").prepend("Flot " + $.plot.version + " &ndash; ");
	});

	</script>
</head>
<body>

	<div id="header">
		<h2>Thresholds</h2>
	</div>

	<div id="content">

		<div class="demo-container">
			<div id="placeholder" class="demo-placeholder"></div>
		</div>

		<p>With the threshold plugin, you can apply a specific color to the part of a data series below a threshold. This is can be useful for highlighting negative values, e.g. when displaying net results or what's in stock.</p>

		<p class="controls">
			<button>Threshold at 5</button>
			<button>Threshold at 0</button>
			<button>Threshold at -2.5</button>
		</p>

	</div>

	<div id="footer">
		Copyright &copy; 2007 - 2013 IOLA and Ole Laursen
	</div>

</body>
</html>
