/*!
 * jQVMap Version 1.0 
 *
 * http://jqvmap.com
 *
 * Copyright 2012, <PERSON> <<EMAIL>>
 * Licensed under the MIT license.
 *
 * Fork Me @ https://github.com/manifestinteractive/jqvmap
 */
.jqvmap-label {
	position: absolute;
	display: none;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	background: #292929;
	color: white;
	font-family: sans-serif, Verdana;
	font-size: smaller;
	padding: 3px;
}
.jqvmap-zoomin, .jqvmap-zoomout {
	position: absolute;
	left: 10px;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	background: #000000;
	padding: 0px 7px;
	color: white;
	cursor: pointer;
	line-height: 20px;
	text-align: center;
}
.jqvmap-zoomin {
	top: 10px;
}
.jqvmap-zoomout {
	top: 40px;
}
.jqvmap-region {
	cursor: pointer;
}
.jqvmap-ajax_response {
	width: 100%;
	height: 500px;
}
