window.CodeMirror=function(){"use strict";function w(a,c){if(!(this instanceof w))return new w(a,c);this.options=c=c||{};for(var d in Tc)!c.hasOwnProperty(d)&&Tc.hasOwnProperty(d)&&(c[d]=Tc[d]);I(c);var e="string"==typeof c.value?0:c.value.first,f=this.display=x(a,e);f.wrapper.CodeMirror=this,F(this),c.autofocus&&!o&&Ib(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,focused:!1,suppressEdits:!1,pasteIncoming:!1,draggingText:!1,highlight:new Le},D(this),c.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap");var g=c.value;"string"==typeof g&&(g=new Zd(c.value,c.mode)),Ab(this,be)(this,g),b&&setTimeout(Ve(Hb,this,!0),20),Kb(this);var h;try{h=document.activeElement==f.input}catch(i){}h||c.autofocus&&!o?setTimeout(Ve(ec,this),20):fc(this),Ab(this,function(){for(var a in Sc)Sc.propertyIsEnumerable(a)&&Sc[a](this,c[a],Vc);for(var b=0;Zc.length>b;++b)Zc[b](this)})()}function x(a,b){var d={},f=d.input=$e("textarea",null,null,"position: absolute; padding: 0; width: 1px; height: 1em; outline: none; font-size: 4px;");return e?f.style.width="1000px":f.setAttribute("wrap","off"),n&&(f.style.border="1px solid black"),f.setAttribute("autocorrect","off"),f.setAttribute("autocapitalize","off"),f.setAttribute("spellcheck","false"),d.inputDiv=$e("div",[f],null,"overflow: hidden; position: relative; width: 3px; height: 0px;"),d.scrollbarH=$e("div",[$e("div",null,null,"height: 1px")],"CodeMirror-hscrollbar"),d.scrollbarV=$e("div",[$e("div",null,null,"width: 1px")],"CodeMirror-vscrollbar"),d.scrollbarFiller=$e("div",null,"CodeMirror-scrollbar-filler"),d.gutterFiller=$e("div",null,"CodeMirror-gutter-filler"),d.lineDiv=$e("div",null,"CodeMirror-code"),d.selectionDiv=$e("div",null,null,"position: relative; z-index: 1"),d.cursor=$e("div","\u00a0","CodeMirror-cursor"),d.otherCursor=$e("div","\u00a0","CodeMirror-cursor CodeMirror-secondarycursor"),d.measure=$e("div",null,"CodeMirror-measure"),d.lineSpace=$e("div",[d.measure,d.selectionDiv,d.lineDiv,d.cursor,d.otherCursor],null,"position: relative; outline: none"),d.mover=$e("div",[$e("div",[d.lineSpace],"CodeMirror-lines")],null,"position: relative"),d.sizer=$e("div",[d.mover],"CodeMirror-sizer"),d.heightForcer=$e("div",null,null,"position: absolute; height: "+Je+"px; width: 1px;"),d.gutters=$e("div",null,"CodeMirror-gutters"),d.lineGutter=null,d.scroller=$e("div",[d.sizer,d.heightForcer,d.gutters],"CodeMirror-scroll"),d.scroller.setAttribute("tabIndex","-1"),d.wrapper=$e("div",[d.inputDiv,d.scrollbarH,d.scrollbarV,d.scrollbarFiller,d.gutterFiller,d.scroller],"CodeMirror"),c&&(d.gutters.style.zIndex=-1,d.scroller.style.paddingRight=0),a.appendChild?a.appendChild(d.wrapper):a(d.wrapper),n&&(f.style.width="0px"),e||(d.scroller.draggable=!0),j?(d.inputDiv.style.height="1px",d.inputDiv.style.position="absolute"):c&&(d.scrollbarH.style.minWidth=d.scrollbarV.style.minWidth="18px"),d.viewOffset=d.lastSizeC=0,d.showingFrom=d.showingTo=b,d.lineNumWidth=d.lineNumInnerWidth=d.lineNumChars=null,d.prevInput="",d.alignWidgets=!1,d.pollingFast=!1,d.poll=new Le,d.cachedCharWidth=d.cachedTextHeight=null,d.measureLineCache=[],d.measureLineCachePos=0,d.inaccurateSelection=!1,d.maxLine=null,d.maxLineLength=0,d.maxLineChanged=!1,d.wheelDX=d.wheelDY=d.wheelStartX=d.wheelStartY=null,d}function y(a){a.doc.mode=w.getMode(a.options,a.doc.modeOption),a.doc.iter(function(a){a.stateAfter&&(a.stateAfter=null),a.styles&&(a.styles=null)}),a.doc.frontier=a.doc.first,_(a,100),a.state.modeGen++,a.curOp&&Db(a)}function z(a){a.options.lineWrapping?(a.display.wrapper.className+=" CodeMirror-wrap",a.display.sizer.style.minWidth=""):(a.display.wrapper.className=a.display.wrapper.className.replace(" CodeMirror-wrap",""),H(a)),B(a),Db(a),mb(a),setTimeout(function(){J(a)},100)}function A(a){var b=vb(a.display),c=a.options.lineWrapping,d=c&&Math.max(5,a.display.scroller.clientWidth/wb(a.display)-3);return function(e){return xd(a.doc,e)?0:c?(Math.ceil(e.text.length/d)||1)*b:b}}function B(a){var b=a.doc,c=A(a);b.iter(function(a){var b=c(a);b!=a.height&&fe(a,b)})}function C(a){var b=bd[a.options.keyMap].style;a.display.wrapper.className=a.display.wrapper.className.replace(/\s*cm-keymap-\S+/g,"")+(b?" cm-keymap-"+b:"")}function D(a){a.display.wrapper.className=a.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+a.options.theme.replace(/(^|\s)\s*/g," cm-s-"),mb(a)}function E(a){F(a),Db(a),setTimeout(function(){L(a)},20)}function F(a){var b=a.display.gutters,c=a.options.gutters;_e(b);for(var d=0;c.length>d;++d){var e=c[d],f=b.appendChild($e("div",null,"CodeMirror-gutter "+e));"CodeMirror-linenumbers"==e&&(a.display.lineGutter=f,f.style.width=(a.display.lineNumWidth||1)+"px")}b.style.display=d?"":"none"}function G(a,b){if(0==b.height)return 0;for(var d,c=b.text.length,e=b;d=ud(e);){var f=d.find();e=ce(a,f.from.line),c+=f.from.ch-f.to.ch}for(e=b;d=vd(e);){var f=d.find();c-=e.text.length-f.from.ch,e=ce(a,f.to.line),c+=e.text.length-f.to.ch}return c}function H(a){var b=a.display,c=a.doc;b.maxLine=ce(c,c.first),b.maxLineLength=G(c,b.maxLine),b.maxLineChanged=!0,c.iter(function(a){var d=G(c,a);d>b.maxLineLength&&(b.maxLineLength=d,b.maxLine=a)})}function I(a){for(var b=!1,c=0;a.gutters.length>c;++c)"CodeMirror-linenumbers"==a.gutters[c]&&(a.lineNumbers?b=!0:a.gutters.splice(c--,1));!b&&a.lineNumbers&&a.gutters.push("CodeMirror-linenumbers")}function J(a){var b=a.display,c=a.doc.height,d=c+eb(b);b.sizer.style.minHeight=b.heightForcer.style.top=d+"px",b.gutters.style.height=Math.max(d,b.scroller.clientHeight-Je)+"px";var e=Math.max(d,b.scroller.scrollHeight),f=b.scroller.scrollWidth>b.scroller.clientWidth,g=e>b.scroller.clientHeight;g?(b.scrollbarV.style.display="block",b.scrollbarV.style.bottom=f?gf(b.measure)+"px":"0",b.scrollbarV.firstChild.style.height=e-b.scroller.clientHeight+b.scrollbarV.clientHeight+"px"):b.scrollbarV.style.display="",f?(b.scrollbarH.style.display="block",b.scrollbarH.style.right=g?gf(b.measure)+"px":"0",b.scrollbarH.firstChild.style.width=b.scroller.scrollWidth-b.scroller.clientWidth+b.scrollbarH.clientWidth+"px"):b.scrollbarH.style.display="",f&&g?(b.scrollbarFiller.style.display="block",b.scrollbarFiller.style.height=b.scrollbarFiller.style.width=gf(b.measure)+"px"):b.scrollbarFiller.style.display="",f&&a.options.coverGutterNextToScrollbar&&a.options.fixedGutter?(b.gutterFiller.style.display="block",b.gutterFiller.style.height=gf(b.measure)+"px",b.gutterFiller.style.width=b.gutters.offsetWidth+"px"):b.gutterFiller.style.display="",k&&0===gf(b.measure)&&(b.scrollbarV.style.minWidth=b.scrollbarH.style.minHeight=l?"18px":"12px")}function K(a,b,c){var d=a.scroller.scrollTop,e=a.wrapper.clientHeight;"number"==typeof c?d=c:c&&(d=c.top,e=c.bottom-c.top),d=Math.floor(d-db(a));var f=Math.ceil(d+e);return{from:he(b,d),to:he(b,f)}}function L(a){var b=a.display;if(b.alignWidgets||b.gutters.firstChild&&a.options.fixedGutter){for(var c=O(b)-b.scroller.scrollLeft+a.doc.scrollLeft,d=b.gutters.offsetWidth,e=c+"px",f=b.lineDiv.firstChild;f;f=f.nextSibling)if(f.alignable)for(var g=0,h=f.alignable;h.length>g;++g)h[g].style.left=e;a.options.fixedGutter&&(b.gutters.style.left=c+d+"px")}}function M(a){if(!a.options.lineNumbers)return!1;var b=a.doc,c=N(a.options,b.first+b.size-1),d=a.display;if(c.length!=d.lineNumChars){var e=d.measure.appendChild($e("div",[$e("div",c)],"CodeMirror-linenumber CodeMirror-gutter-elt")),f=e.firstChild.offsetWidth,g=e.offsetWidth-f;return d.lineGutter.style.width="",d.lineNumInnerWidth=Math.max(f,d.lineGutter.offsetWidth-g),d.lineNumWidth=d.lineNumInnerWidth+g,d.lineNumChars=d.lineNumInnerWidth?c.length:-1,d.lineGutter.style.width=d.lineNumWidth+"px",!0}return!1}function N(a,b){return a.lineNumberFormatter(b+a.firstLineNumber)+""}function O(a){return cf(a.scroller).left-cf(a.sizer).left}function P(a,b,c){for(var f,d=a.display.showingFrom,e=a.display.showingTo,g=K(a.display,a.doc,c);Q(a,b,g)&&(f=!0,X(a),J(a),c&&(c=Math.min(a.display.scroller.scrollHeight-a.display.scroller.clientHeight,"number"==typeof c?c:c.top)),g=K(a.display,a.doc,c),!(g.from>=a.display.showingFrom&&g.to<=a.display.showingTo));)b=[];return f&&(Ge(a,"update",a),(a.display.showingFrom!=d||a.display.showingTo!=e)&&Ge(a,"viewportChange",a,a.display.showingFrom,a.display.showingTo)),f}function Q(a,b,d){var e=a.display,f=a.doc;if(!e.wrapper.clientWidth)return e.showingFrom=e.showingTo=f.first,e.viewOffset=0,void 0;if(!(0==b.length&&d.from>e.showingFrom&&d.to<e.showingTo)){M(a)&&(b=[{from:f.first,to:f.first+f.size}]);var g=e.sizer.style.marginLeft=e.gutters.offsetWidth+"px";e.scrollbarH.style.left=a.options.fixedGutter?g:"0";var h=1/0;if(a.options.lineNumbers)for(var i=0;b.length>i;++i)if(b[i].diff){h=b[i].from;break}var j=f.first+f.size,k=Math.max(d.from-a.options.viewportMargin,f.first),l=Math.min(j,d.to+a.options.viewportMargin);if(k>e.showingFrom&&20>k-e.showingFrom&&(k=Math.max(f.first,e.showingFrom)),e.showingTo>l&&20>e.showingTo-l&&(l=Math.min(j,e.showingTo)),v)for(k=ge(wd(f,ce(f,k)));j>l&&xd(f,ce(f,l));)++l;var m=[{from:Math.max(e.showingFrom,f.first),to:Math.min(e.showingTo,j)}];if(m=m[0].from>=m[0].to?[]:S(m,b),v)for(var i=0;m.length>i;++i)for(var o,n=m[i];o=vd(ce(f,n.to-1));){var p=o.find().from.line;if(!(p>n.from)){m.splice(i--,1);break}n.to=p}for(var q=0,i=0;m.length>i;++i){var n=m[i];k>n.from&&(n.from=k),n.to>l&&(n.to=l),n.from>=n.to?m.splice(i--,1):q+=n.to-n.from}if(q==l-k&&k==e.showingFrom&&l==e.showingTo)return R(a),void 0;m.sort(function(a,b){return a.from-b.from});try{var r=document.activeElement}catch(s){}.7*(l-k)>q&&(e.lineDiv.style.display="none"),U(a,k,l,m,h),e.lineDiv.style.display="",r&&document.activeElement!=r&&r.offsetHeight&&r.focus();var t=k!=e.showingFrom||l!=e.showingTo||e.lastSizeC!=e.wrapper.clientHeight;t&&(e.lastSizeC=e.wrapper.clientHeight),e.showingFrom=k,e.showingTo=l,_(a,100);for(var x,u=e.lineDiv.offsetTop,w=e.lineDiv.firstChild;w;w=w.nextSibling)if(w.lineObj){if(c){var y=w.offsetTop+w.offsetHeight;x=y-u,u=y}else{var z=cf(w);x=z.bottom-z.top}var A=w.lineObj.height-x;if(2>x&&(x=vb(e)),A>.001||-.001>A){fe(w.lineObj,x);var B=w.lineObj.widgets;if(B)for(var i=0;B.length>i;++i)B[i].height=B[i].node.offsetHeight}}return R(a),!0}}function R(a){var b=a.display.viewOffset=ie(a,ce(a.doc,a.display.showingFrom));a.display.mover.style.top=b+"px"}function S(a,b){for(var c=0,d=b.length||0;d>c;++c){for(var e=b[c],f=[],g=e.diff||0,h=0,i=a.length;i>h;++h){var j=a[h];e.to<=j.from&&e.diff?f.push({from:j.from+g,to:j.to+g}):e.to<=j.from||e.from>=j.to?f.push(j):(e.from>j.from&&f.push({from:j.from,to:e.from}),e.to<j.to&&f.push({from:e.to+g,to:j.to+g}))}a=f}return a}function T(a){for(var b=a.display,c={},d={},e=b.gutters.firstChild,f=0;e;e=e.nextSibling,++f)c[a.options.gutters[f]]=e.offsetLeft,d[a.options.gutters[f]]=e.offsetWidth;return{fixedPos:O(b),gutterTotalWidth:b.gutters.offsetWidth,gutterLeft:c,gutterWidth:d,wrapperWidth:b.wrapper.clientWidth}}function U(a,b,c,d,f){function l(b){var c=b.nextSibling;return e&&p&&a.display.currentWheelTarget==b?(b.style.display="none",b.lineObj=null):b.parentNode.removeChild(b),c}var g=T(a),h=a.display,i=a.options.lineNumbers;d.length||e&&a.display.currentWheelTarget||_e(h.lineDiv);var j=h.lineDiv,k=j.firstChild,m=d.shift(),n=b;for(a.doc.iter(b,c,function(b){if(m&&m.to==n&&(m=d.shift()),xd(a.doc,b)){if(0!=b.height&&fe(b,0),b.widgets&&k.previousSibling)for(var c=0;b.widgets.length>c;++c)if(b.widgets[c].showIfHidden){var e=k.previousSibling;if(/pre/i.test(e.nodeName)){var h=$e("div",null,null,"position: relative");e.parentNode.replaceChild(h,e),h.appendChild(e),e=h}var o=e.appendChild($e("div",[b.widgets[c].node],"CodeMirror-linewidget"));W(b.widgets[c],o,e,g)}}else if(m&&n>=m.from&&m.to>n){for(;k.lineObj!=b;)k=l(k);i&&n>=f&&k.lineNumber&&bf(k.lineNumber,N(a.options,n)),k=k.nextSibling}else{if(b.widgets)for(var r,p=0,q=k;q&&20>p;++p,q=q.nextSibling)if(q.lineObj==b&&/div/i.test(q.nodeName)){r=q;break}var s=V(a,b,n,g,r);if(s!=r)j.insertBefore(s,k);else{for(;k!=r;)k=l(k);k=k.nextSibling}s.lineObj=b}++n});k;)k=l(k)}function V(a,b,d,e,f){var j,g=Od(a,b),h=b.gutterMarkers,i=a.display;if(!(a.options.lineNumbers||h||b.bgClass||b.wrapClass||b.widgets))return g;if(f){f.alignable=null;for(var n,k=!0,l=0,m=f.firstChild;m;m=n)if(n=m.nextSibling,/\bCodeMirror-linewidget\b/.test(m.className)){for(var o=0,p=!0;b.widgets.length>o;++o){var q=b.widgets[o],r=!1;if(q.above||(r=p,p=!1),q.node==m.firstChild){W(q,m,f,e),++l,r&&f.insertBefore(g,m);break}}if(o==b.widgets.length){k=!1;break}}else f.removeChild(m);k&&l==b.widgets.length&&(j=f,f.className=b.wrapClass||"")}if(j||(j=$e("div",null,b.wrapClass,"position: relative"),j.appendChild(g)),b.bgClass&&j.insertBefore($e("div",null,b.bgClass+" CodeMirror-linebackground"),j.firstChild),a.options.lineNumbers||h){var s=j.insertBefore($e("div",null,null,"position: absolute; left: "+(a.options.fixedGutter?e.fixedPos:-e.gutterTotalWidth)+"px"),j.firstChild);if(a.options.fixedGutter&&(j.alignable||(j.alignable=[])).push(s),!a.options.lineNumbers||h&&h["CodeMirror-linenumbers"]||(j.lineNumber=s.appendChild($e("div",N(a.options,d),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+e.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+i.lineNumInnerWidth+"px"))),h)for(var t=0;a.options.gutters.length>t;++t){var u=a.options.gutters[t],v=h.hasOwnProperty(u)&&h[u];v&&s.appendChild($e("div",[v],"CodeMirror-gutter-elt","left: "+e.gutterLeft[u]+"px; width: "+e.gutterWidth[u]+"px"))}}if(c&&(j.style.zIndex=2),b.widgets&&j!=f)for(var o=0,w=b.widgets;w.length>o;++o){var q=w[o],x=$e("div",[q.node],"CodeMirror-linewidget");W(q,x,j,e),q.above?j.insertBefore(x,a.options.lineNumbers&&0!=b.height?s:g):j.appendChild(x),Ge(q,"redraw")}return j}function W(a,b,c,d){if(a.noHScroll){(c.alignable||(c.alignable=[])).push(b);var e=d.wrapperWidth;b.style.left=d.fixedPos+"px",a.coverGutter||(e-=d.gutterTotalWidth,b.style.paddingLeft=d.gutterTotalWidth+"px"),b.style.width=e+"px"}a.coverGutter&&(b.style.zIndex=5,b.style.position="relative",a.noHScroll||(b.style.marginLeft=-d.gutterTotalWidth+"px"))}function X(a){var b=a.display,c=uc(a.doc.sel.from,a.doc.sel.to);if(c||a.options.showCursorWhenSelecting?Y(a):b.cursor.style.display=b.otherCursor.style.display="none",c?b.selectionDiv.style.display="none":Z(a),a.options.moveInputWithCursor){var d=qb(a,a.doc.sel.head,"div"),e=cf(b.wrapper),f=cf(b.lineDiv);b.inputDiv.style.top=Math.max(0,Math.min(b.wrapper.clientHeight-10,d.top+f.top-e.top))+"px",b.inputDiv.style.left=Math.max(0,Math.min(b.wrapper.clientWidth-10,d.left+f.left-e.left))+"px"}}function Y(a){var b=a.display,c=qb(a,a.doc.sel.head,"div");b.cursor.style.left=c.left+"px",b.cursor.style.top=c.top+"px",b.cursor.style.height=Math.max(0,c.bottom-c.top)*a.options.cursorHeight+"px",b.cursor.style.display="",c.other?(b.otherCursor.style.display="",b.otherCursor.style.left=c.other.left+"px",b.otherCursor.style.top=c.other.top+"px",b.otherCursor.style.height=.85*(c.other.bottom-c.other.top)+"px"):b.otherCursor.style.display="none"}function Z(a){function h(a,b,c,d){0>b&&(b=0),e.appendChild($e("div",null,"CodeMirror-selected","position: absolute; left: "+a+"px; top: "+b+"px; width: "+(null==c?f-a:c)+"px; height: "+(d-b)+"px"))}function i(b,d,e,i){function m(c){return pb(a,tc(b,c),"div",j)}var j=ce(c,b),k=j.text.length,l=i?1/0:-1/0;return of(je(j),d||0,null==e?k:e,function(a,b,c){var n,o,p,j=m(a);if(a==b)n=j,o=p=j.left;else{if(n=m(b-1),"rtl"==c){var q=j;j=n,n=q}o=j.left,p=n.right}n.top-j.top>3&&(h(o,j.top,null,j.bottom),o=g,j.bottom<n.top&&h(o,j.bottom,null,n.top)),null==e&&b==k&&(p=f),null==d&&0==a&&(o=g),l=i?Math.min(n.top,l):Math.max(n.bottom,l),g+1>o&&(o=g),h(o,n.top,p-o,n.bottom)}),l}var b=a.display,c=a.doc,d=a.doc.sel,e=document.createDocumentFragment(),f=b.lineSpace.offsetWidth,g=fb(a.display);if(d.from.line==d.to.line)i(d.from.line,d.from.ch,d.to.ch);else{for(var l,n,j=ce(c,d.from.line),k=j,m=[d.from.line,d.from.ch];l=vd(k);){var o=l.find();if(m.push(o.from.ch,o.to.line,o.to.ch),o.to.line==d.to.line){m.push(d.to.ch),n=!0;break}k=ce(c,o.to.line)}if(n)for(var p=0;m.length>p;p+=3)i(m[p],m[p+1],m[p+2]);else{var q,r,s=ce(c,d.to.line);q=d.from.ch?i(d.from.line,d.from.ch,null,!1):ie(a,j)-b.viewOffset,r=d.to.ch?i(d.to.line,ud(s)?null:0,d.to.ch,!0):ie(a,s)-b.viewOffset,r>q&&h(g,q,null,r)}}af(b.selectionDiv,e),b.selectionDiv.style.display=""}function $(a){if(a.state.focused){var b=a.display;clearInterval(b.blinker);var c=!0;b.cursor.style.visibility=b.otherCursor.style.visibility="",b.blinker=setInterval(function(){b.cursor.style.visibility=b.otherCursor.style.visibility=(c=!c)?"":"hidden"},a.options.cursorBlinkRate)}}function _(a,b){a.doc.mode.startState&&a.doc.frontier<a.display.showingTo&&a.state.highlight.set(b,Ve(ab,a))}function ab(a){var b=a.doc;if(b.frontier<b.first&&(b.frontier=b.first),!(b.frontier>=a.display.showingTo)){var f,c=+new Date+a.options.workTime,d=$c(b.mode,cb(a,b.frontier)),e=[];b.iter(b.frontier,Math.min(b.first+b.size,a.display.showingTo+500),function(g){if(b.frontier>=a.display.showingFrom){var h=g.styles;g.styles=Jd(a,g,d);for(var i=!h||h.length!=g.styles.length,j=0;!i&&h.length>j;++j)i=h[j]!=g.styles[j];i&&(f&&f.end==b.frontier?f.end++:e.push(f={start:b.frontier,end:b.frontier+1})),g.stateAfter=$c(b.mode,d)}else Ld(a,g,d),g.stateAfter=0==b.frontier%5?$c(b.mode,d):null;return++b.frontier,+new Date>c?(_(a,a.options.workDelay),!0):void 0}),e.length&&Ab(a,function(){for(var a=0;e.length>a;++a)Db(this,e[a].start,e[a].end)})()}}function bb(a,b){for(var c,d,e=a.doc,f=b,g=b-100;f>g;--f){if(e.first>=f)return e.first;var h=ce(e,f-1);if(h.stateAfter)return f;var i=Me(h.text,null,a.options.tabSize);(null==d||c>i)&&(d=f-1,c=i)}return d}function cb(a,b){var c=a.doc,d=a.display;if(!c.mode.startState)return!0;var e=bb(a,b),f=e>c.first&&ce(c,e-1).stateAfter;return f=f?$c(c.mode,f):_c(c.mode),c.iter(e,b,function(g){Ld(a,g,f);var h=e==b-1||0==e%5||e>=d.showingFrom&&d.showingTo>e;g.stateAfter=h?$c(c.mode,f):null,++e}),f}function db(a){return a.lineSpace.offsetTop}function eb(a){return a.mover.offsetHeight-a.lineSpace.offsetHeight}function fb(a){var b=af(a.measure,$e("pre",null,null,"text-align: left")).appendChild($e("span","x"));return b.offsetLeft}function gb(a,b,c,d){var e=-1;d=d||jb(a,b);for(var f=c;;f+=e){var g=d[f];if(g)break;0>e&&0==f&&(e=1)}return{left:c>f?g.right:g.left,right:f>c?g.left:g.right,top:g.top,bottom:g.bottom}}function hb(a,b){for(var c=a.display.measureLineCache,d=0;c.length>d;++d){var e=c[d];if(e.text==b.text&&e.markedSpans==b.markedSpans&&a.display.scroller.clientWidth==e.width&&e.classes==b.textClass+"|"+b.bgClass+"|"+b.wrapClass)return e}}function ib(a,b){var c=hb(a,b);c&&(c.text=c.measure=c.markedSpans=null)}function jb(a,b){var c=hb(a,b);if(c)return c.measure;var d=kb(a,b),e=a.display.measureLineCache,f={text:b.text,width:a.display.scroller.clientWidth,markedSpans:b.markedSpans,measure:d,classes:b.textClass+"|"+b.bgClass+"|"+b.wrapClass};return 16==e.length?e[++a.display.measureLineCachePos%16]=f:e.push(f),d}function kb(a,e){var f=a.display,g=Ue(e.text.length),h=Od(a,e,g);if(b&&!c&&!a.options.lineWrapping&&h.childNodes.length>100){for(var i=document.createDocumentFragment(),j=10,k=h.childNodes.length,l=0,m=Math.ceil(k/j);m>l;++l){for(var n=$e("div",null,null,"display: inline-block"),o=0;j>o&&k;++o)n.appendChild(h.firstChild),--k;i.appendChild(n)}h.appendChild(i)}af(f.measure,h);var p=cf(f.lineDiv),q=[],r=Ue(e.text.length),s=h.offsetHeight;d&&f.measure.first!=h&&af(f.measure,h);for(var t,l=0;g.length>l;++l)if(t=g[l]){for(var u=cf(t),v=Math.max(0,u.top-p.top),w=Math.min(u.bottom-p.top,s),o=0;q.length>o;o+=2){var x=q[o],y=q[o+1];if(!(x>w||v>y)&&(v>=x&&y>=w||x>=v&&w>=y||Math.min(w,y)-Math.max(v,x)>=w-v>>1)){q[o]=Math.min(v,x),q[o+1]=Math.max(w,y);break}}o==q.length&&q.push(v,w);var z=u.right;t.measureRight&&(z=cf(t.measureRight).left),r[l]={left:u.left-p.left,right:z-p.left,top:o}}for(var t,l=0;r.length>l;++l)if(t=r[l]){var A=t.top;t.top=q[A],t.bottom=q[A+1]}return r}function lb(a,b){var c=!1;if(b.markedSpans)for(var d=0;b.markedSpans>d;++d){var e=b.markedSpans[d];!e.collapsed||null!=e.to&&e.to!=b.text.length||(c=!0)}var f=!c&&hb(a,b);if(f)return gb(a,b,b.text.length,f.measure).right;var g=Od(a,b),h=g.appendChild(jf(a.display.measure));return af(a.display.measure,g),cf(h).right-cf(a.display.lineDiv).left}function mb(a){a.display.measureLineCache.length=a.display.measureLineCachePos=0,a.display.cachedCharWidth=a.display.cachedTextHeight=null,a.options.lineWrapping||(a.display.maxLineChanged=!0),a.display.lineNumChars=null}function nb(a,b,c,d){if(b.widgets)for(var e=0;b.widgets.length>e;++e)if(b.widgets[e].above){var f=Dd(b.widgets[e]);c.top+=f,c.bottom+=f}if("line"==d)return c;d||(d="local");var g=ie(a,b);if("local"!=d&&(g-=a.display.viewOffset),"page"==d){var h=cf(a.display.lineSpace);g+=h.top+(window.pageYOffset||(document.documentElement||document.body).scrollTop);var i=h.left+(window.pageXOffset||(document.documentElement||document.body).scrollLeft);c.left+=i,c.right+=i}return c.top+=g,c.bottom+=g,c}function ob(a,b,c){if("div"==c)return b;var d=b.left,e=b.top;"page"==c&&(d-=window.pageXOffset||(document.documentElement||document.body).scrollLeft,e-=window.pageYOffset||(document.documentElement||document.body).scrollTop);var f=cf(a.display.lineSpace);if(d-=f.left,e-=f.top,"local"==c||!c){var g=cf(a.display.wrapper);d+=g.left,e+=g.top}return{left:d,top:e}}function pb(a,b,c,d){return d||(d=ce(a.doc,b.line)),nb(a,d,gb(a,d,b.ch),c)}function qb(a,b,c,d,e){function f(b,f){var g=gb(a,d,b,e);return f?g.left=g.right:g.right=g.left,nb(a,d,g,c)}function g(a,b){var c=h[b],d=c.level%2;return a==pf(c)&&b&&c.level<h[b-1].level?(c=h[--b],a=qf(c)-(c.level%2?0:1),d=!0):a==qf(c)&&h.length-1>b&&c.level<h[b+1].level&&(c=h[++b],a=pf(c)-c.level%2,d=!1),d&&a==c.to&&a>c.from?f(a-1):f(a,d)}d=d||ce(a.doc,b.line),e||(e=jb(a,d));var h=je(d),i=b.ch;if(!h)return f(i);var j=xf(h,i),k=g(i,j);return null!=wf&&(k.other=g(i,wf)),k}function rb(a,b,c){var d=new tc(a,b);return c&&(d.outside=!0),d}function sb(a,b,c){var d=a.doc;if(c+=a.display.viewOffset,0>c)return rb(d.first,0,!0);var e=he(d,c),f=d.first+d.size-1;if(e>f)return rb(d.first+d.size-1,ce(d,f).text.length,!0);for(0>b&&(b=0);;){var g=ce(d,e),h=tb(a,g,e,b,c),i=vd(g),j=i&&i.find();if(!(i&&h.ch>=j.from.ch))return h;e=j.to.line}}function tb(a,b,c,d,e){function j(d){var e=qb(a,tc(c,d),"line",b,i);return g=!0,f>e.bottom?e.left-h:e.top>f?e.left+h:(g=!1,e.left)}var f=e-ie(a,b),g=!1,h=2*a.display.wrapper.clientWidth,i=jb(a,b),k=je(b),l=b.text.length,m=rf(b),n=sf(b),o=j(m),p=g,q=j(n),r=g;if(d>q)return rb(c,n,r);for(;;){if(k?n==m||n==zf(b,m,1):1>=n-m){for(var s=q-d>d-o,t=s?m:n;Ze.test(b.text.charAt(t));)++t;var u=rb(c,t,s?p:r);return u.after=s,u}var v=Math.ceil(l/2),w=m+v;if(k){w=m;for(var x=0;v>x;++x)w=zf(b,w,1)}var y=j(w);y>d?(n=w,q=y,(r=g)&&(q+=1e3),l=v):(m=w,o=y,p=g,l-=v)}}function vb(a){if(null!=a.cachedTextHeight)return a.cachedTextHeight;if(null==ub){ub=$e("pre");for(var b=0;49>b;++b)ub.appendChild(document.createTextNode("x")),ub.appendChild($e("br"));ub.appendChild(document.createTextNode("x"))}af(a.measure,ub);var c=ub.offsetHeight/50;return c>3&&(a.cachedTextHeight=c),_e(a.measure),c||1}function wb(a){if(null!=a.cachedCharWidth)return a.cachedCharWidth;var b=$e("span","x"),c=$e("pre",[b]);af(a.measure,c);var d=b.offsetWidth;return d>2&&(a.cachedCharWidth=d),d||10}function yb(a){a.curOp={changes:[],updateInput:null,userSelChange:null,textChanged:null,selectionChanged:!1,cursorActivity:!1,updateMaxLine:!1,updateScrollPos:!1,id:++xb},Fe++||(Ee=[])}function zb(a){var b=a.curOp,c=a.doc,d=a.display;if(a.curOp=null,b.updateMaxLine&&H(a),d.maxLineChanged&&!a.options.lineWrapping&&d.maxLine){var e=lb(a,d.maxLine);d.sizer.style.minWidth=Math.max(0,e+3+Je)+"px",d.maxLineChanged=!1;var f=Math.max(0,d.sizer.offsetLeft+d.sizer.offsetWidth-d.scroller.clientWidth);c.scrollLeft>f&&!b.updateScrollPos&&Ub(a,Math.min(d.scroller.scrollLeft,f),!0)}var g,h;if(b.updateScrollPos)g=b.updateScrollPos;else if(b.selectionChanged&&d.scroller.clientHeight){var i=qb(a,c.sel.head);g=Jc(a,i.left,i.top,i.left,i.bottom)}(b.changes.length||g&&null!=g.scrollTop)&&(h=P(a,b.changes,g&&g.scrollTop),a.display.scroller.offsetHeight&&(a.doc.scrollTop=a.display.scroller.scrollTop)),!h&&b.selectionChanged&&X(a),b.updateScrollPos?(d.scroller.scrollTop=d.scrollbarV.scrollTop=c.scrollTop=g.scrollTop,d.scroller.scrollLeft=d.scrollbarH.scrollLeft=c.scrollLeft=g.scrollLeft,L(a),b.scrollToPos&&Hc(a,yc(a.doc,b.scrollToPos),b.scrollToPosMargin)):g&&Gc(a),b.selectionChanged&&$(a),a.state.focused&&b.updateInput&&Hb(a,b.userSelChange);var j=b.maybeHiddenMarkers,k=b.maybeUnhiddenMarkers;if(j)for(var l=0;j.length>l;++l)j[l].lines.length||De(j[l],"hide");if(k)for(var l=0;k.length>l;++l)k[l].lines.length&&De(k[l],"unhide");var m;if(--Fe||(m=Ee,Ee=null),b.textChanged&&De(a,"change",a,b.textChanged),b.cursorActivity&&De(a,"cursorActivity",a),m)for(var l=0;m.length>l;++l)m[l]()}function Ab(a,b){return function(){var c=a||this,d=!c.curOp;d&&yb(c);try{var e=b.apply(c,arguments)}finally{d&&zb(c)}return e}}function Bb(a){return function(){var c,b=this.cm&&!this.cm.curOp;b&&yb(this.cm);try{c=a.apply(this,arguments)}finally{b&&zb(this.cm)}return c}}function Cb(a,b){var d,c=!a.curOp;c&&yb(a);try{d=b()}finally{c&&zb(a)}return d}function Db(a,b,c,d){null==b&&(b=a.doc.first),null==c&&(c=a.doc.first+a.doc.size),a.curOp.changes.push({from:b,to:c,diff:d})}function Eb(a){a.display.pollingFast||a.display.poll.set(a.options.pollInterval,function(){Gb(a),a.state.focused&&Eb(a)})}function Fb(a){function c(){var d=Gb(a);d||b?(a.display.pollingFast=!1,Eb(a)):(b=!0,a.display.poll.set(60,c))}var b=!1;a.display.pollingFast=!0,a.display.poll.set(20,c)}function Gb(a){var c=a.display.input,e=a.display.prevInput,f=a.doc,g=f.sel;if(!a.state.focused||lf(c)||Jb(a))return!1;var h=c.value;if(h==e&&uc(g.from,g.to))return!1;if(b&&!d&&a.display.inputHasSelection===h)return Hb(a,!0),!1;var i=!a.curOp;i&&yb(a),g.shift=!1;for(var j=0,k=Math.min(e.length,h.length);k>j&&e.charCodeAt(j)==h.charCodeAt(j);)++j;var l=g.from,m=g.to;e.length>j?l=tc(l.line,l.ch-(e.length-j)):a.state.overwrite&&uc(l,m)&&!a.state.pasteIncoming&&(m=tc(m.line,Math.min(ce(f,m.line).text.length,m.ch+(h.length-j))));var n=a.curOp.updateInput;return mc(a.doc,{from:l,to:m,text:kf(h.slice(j)),origin:a.state.pasteIncoming?"paste":"+input"},"end"),a.curOp.updateInput=n,h.length>1e3||h.indexOf("\n")>-1?c.value=a.display.prevInput="":a.display.prevInput=h,i&&zb(a),a.state.pasteIncoming=!1,!0}function Hb(a,c){var e,f,g=a.doc;if(uc(g.sel.from,g.sel.to))c&&(a.display.prevInput=a.display.input.value="",b&&!d&&(a.display.inputHasSelection=null));else{a.display.prevInput="",e=mf&&(g.sel.to.line-g.sel.from.line>100||(f=a.getSelection()).length>1e3);var h=e?"-":f||a.getSelection();a.display.input.value=h,a.state.focused&&Qe(a.display.input),b&&!d&&(a.display.inputHasSelection=h)}a.display.inaccurateSelection=e}function Ib(a){"nocursor"==a.options.readOnly||o&&document.activeElement==a.display.input||a.display.input.focus()}function Jb(a){return a.options.readOnly||a.doc.cantEdit}function Kb(a){function d(){a.state.focused&&setTimeout(Ve(Ib,a),0)}function f(){e.set(function(){c.cachedCharWidth=c.cachedTextHeight=null,mb(a),Cb(a,Ve(Db,a))},200)}function g(){for(var a=c.wrapper.parentNode;a&&a!=document.body;a=a.parentNode);a?setTimeout(g,5e3):Ce(window,"resize",f)}function h(b){a.options.onDragEvent&&a.options.onDragEvent(a,ve(b))||ye(b)}function i(){c.inaccurateSelection&&(c.prevInput="",c.inaccurateSelection=!1,c.input.value=a.getSelection(),Qe(c.input))}var c=a.display;Be(c.scroller,"mousedown",Ab(a,Pb)),b?Be(c.scroller,"dblclick",Ab(a,function(b){var c=Mb(a,b);if(c&&!Rb(a,b)&&!Lb(a.display,b)){we(b);var d=Qc(ce(a.doc,c.line).text,c);Bc(a.doc,d.from,d.to)}})):Be(c.scroller,"dblclick",we),Be(c.lineSpace,"selectstart",function(a){Lb(c,a)||we(a)}),t||Be(c.scroller,"contextmenu",function(b){hc(a,b)}),Be(c.scroller,"scroll",function(){c.scroller.clientHeight&&(Tb(a,c.scroller.scrollTop),Ub(a,c.scroller.scrollLeft,!0),De(a,"scroll",a))}),Be(c.scrollbarV,"scroll",function(){c.scroller.clientHeight&&Tb(a,c.scrollbarV.scrollTop)}),Be(c.scrollbarH,"scroll",function(){c.scroller.clientHeight&&Ub(a,c.scrollbarH.scrollLeft)}),Be(c.scroller,"mousewheel",function(b){Xb(a,b)}),Be(c.scroller,"DOMMouseScroll",function(b){Xb(a,b)}),Be(c.scrollbarH,"mousedown",d),Be(c.scrollbarV,"mousedown",d),Be(c.wrapper,"scroll",function(){c.wrapper.scrollTop=c.wrapper.scrollLeft=0});var e=new Le;Be(window,"resize",f),setTimeout(g,5e3),Be(c.input,"keyup",Ab(a,function(b){a.options.onKeyEvent&&a.options.onKeyEvent(a,ve(b))||16==b.keyCode&&(a.doc.sel.shift=!1)})),Be(c.input,"input",Ve(Fb,a)),Be(c.input,"keydown",Ab(a,cc)),Be(c.input,"keypress",Ab(a,dc)),Be(c.input,"focus",Ve(ec,a)),Be(c.input,"blur",Ve(fc,a)),a.options.dragDrop&&(Be(c.scroller,"dragstart",function(b){Sb(a,b)}),Be(c.scroller,"dragenter",h),Be(c.scroller,"dragover",h),Be(c.scroller,"drop",Ab(a,Qb))),Be(c.scroller,"paste",function(b){Lb(c,b)||(Ib(a),Fb(a))}),Be(c.input,"paste",function(){a.state.pasteIncoming=!0,Fb(a)}),Be(c.input,"cut",i),Be(c.input,"copy",i),j&&Be(c.sizer,"mouseup",function(){document.activeElement==c.input&&c.input.blur(),Ib(a)})}function Lb(a,b){for(var c=ze(b);c!=a.wrapper;c=c.parentNode){if(!c)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(c.className)||c.parentNode==a.sizer&&c!=a.mover)return!0}}function Mb(a,b,c){var d=a.display;if(!c){var e=ze(b);if(e==d.scrollbarH||e==d.scrollbarH.firstChild||e==d.scrollbarV||e==d.scrollbarV.firstChild||e==d.scrollbarFiller||e==d.gutterFiller)return null}var f,g,h=cf(d.lineSpace);try{f=b.clientX,g=b.clientY}catch(b){return null}return sb(a,f-h.left,g-h.top)}function Pb(a){function q(a){if(!uc(p,a)){if(p=a,"single"==j)return Bc(c.doc,yc(f,h),a),void 0;if(n=yc(f,n),o=yc(f,o),"double"==j){var b=Qc(ce(f,a.line).text,a);vc(a,n)?Bc(c.doc,b.from,o):Bc(c.doc,n,b.to)}else"triple"==j&&(vc(a,n)?Bc(c.doc,o,yc(f,tc(a.line,0))):Bc(c.doc,n,yc(f,tc(a.line+1,0))))}}function u(a){var b=++s,e=Mb(c,a,!0);if(e)if(uc(e,l)){var h=a.clientY<r.top?-20:a.clientY>r.bottom?20:0;h&&setTimeout(Ab(c,function(){s==b&&(d.scroller.scrollTop+=h,u(a))}),50)}else{c.state.focused||ec(c),l=e,q(e);var g=K(d,f);(e.line>=g.to||e.line<g.from)&&setTimeout(Ab(c,function(){s==b&&u(a)}),150)}}function v(a){s=1/0,we(a),Ib(c),Ce(document,"mousemove",w),Ce(document,"mouseup",x)}var c=this,d=c.display,f=c.doc,g=f.sel;if(g.shift=a.shiftKey,Lb(d,a))return e||(d.scroller.draggable=!1,setTimeout(function(){d.scroller.draggable=!0},100)),void 0;if(!Rb(c,a)){var h=Mb(c,a);switch(Ae(a)){case 3:return t&&hc.call(c,c,a),void 0;case 2:return h&&Bc(c.doc,h),setTimeout(Ve(Ib,c),20),we(a),void 0}if(!h)return ze(a)==d.scroller&&we(a),void 0;c.state.focused||ec(c);var i=+new Date,j="single";if(Ob&&Ob.time>i-400&&uc(Ob.pos,h))j="triple",we(a),setTimeout(Ve(Ib,c),20),Rc(c,h.line);else if(Nb&&Nb.time>i-400&&uc(Nb.pos,h)){j="double",Ob={time:i,pos:h},we(a);var k=Qc(ce(f,h.line).text,h);Bc(c.doc,k.from,k.to)}else Nb={time:i,pos:h};var l=h;if(c.options.dragDrop&&df&&!Jb(c)&&!uc(g.from,g.to)&&!vc(h,g.from)&&!vc(g.to,h)&&"single"==j){var m=Ab(c,function(b){e&&(d.scroller.draggable=!1),c.state.draggingText=!1,Ce(document,"mouseup",m),Ce(d.scroller,"drop",m),10>Math.abs(a.clientX-b.clientX)+Math.abs(a.clientY-b.clientY)&&(we(b),Bc(c.doc,h),Ib(c))});return e&&(d.scroller.draggable=!0),c.state.draggingText=m,d.scroller.dragDrop&&d.scroller.dragDrop(),Be(document,"mouseup",m),Be(d.scroller,"drop",m),void 0}we(a),"single"==j&&Bc(c.doc,yc(f,h));var n=g.from,o=g.to,p=h,r=cf(d.wrapper),s=0,w=Ab(c,function(a){b||Ae(a)?u(a):v(a)}),x=Ab(c,v);Be(document,"mousemove",w),Be(document,"mouseup",x)}}function Qb(a){var b=this;if(!(Lb(b.display,a)||b.options.onDragEvent&&b.options.onDragEvent(b,ve(a)))){we(a);var c=Mb(b,a,!0),d=a.dataTransfer.files;if(c&&!Jb(b))if(d&&d.length&&window.FileReader&&window.File)for(var e=d.length,f=Array(e),g=0,h=function(a,d){var h=new FileReader;h.onload=function(){f[d]=h.result,++g==e&&(c=yc(b.doc,c),mc(b.doc,{from:c,to:c,text:kf(f.join("\n")),origin:"paste"},"around"))
},h.readAsText(a)},i=0;e>i;++i)h(d[i],i);else{if(b.state.draggingText&&!vc(c,b.doc.sel.from)&&!vc(b.doc.sel.to,c))return b.state.draggingText(a),setTimeout(Ve(Ib,b),20),void 0;try{var f=a.dataTransfer.getData("Text");if(f){var j=b.doc.sel.from,k=b.doc.sel.to;Dc(b.doc,c,c),b.state.draggingText&&sc(b.doc,"",j,k,"paste"),b.replaceSelection(f,null,"paste"),Ib(b),ec(b)}}catch(a){}}}}function Rb(a,b){var c=a.display;try{var d=b.clientX,e=b.clientY}catch(b){return!1}if(d>=Math.floor(cf(c.gutters).right))return!1;if(we(b),!Ie(a,"gutterClick"))return!0;var f=cf(c.lineDiv);if(e>f.bottom)return!0;e-=f.top-c.viewOffset;for(var g=0;a.options.gutters.length>g;++g){var h=c.gutters.childNodes[g];if(h&&cf(h).right>=d){var i=he(a.doc,e),j=a.options.gutters[g];Ge(a,"gutterClick",a,i,j,b);break}}return!0}function Sb(a,c){if(b&&!a.state.draggingText)return ye(c),void 0;if(!Lb(a.display,c)){var d=a.getSelection();if(c.dataTransfer.setData("Text",d),c.dataTransfer.setDragImage){var e=$e("img",null,null,"position: fixed; left: 0; top: 0;");h&&(e.width=e.height=1,a.display.wrapper.appendChild(e),e._top=e.offsetTop),i&&(a.display.dragImg?e=a.display.dragImg:(a.display.dragImg=e,e.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",a.display.wrapper.appendChild(e))),c.dataTransfer.setDragImage(e,0,0),h&&e.parentNode.removeChild(e)}}}function Tb(b,c){2>Math.abs(b.doc.scrollTop-c)||(b.doc.scrollTop=c,a||P(b,[],c),b.display.scroller.scrollTop!=c&&(b.display.scroller.scrollTop=c),b.display.scrollbarV.scrollTop!=c&&(b.display.scrollbarV.scrollTop=c),a&&P(b,[]))}function Ub(a,b,c){(c?b==a.doc.scrollLeft:2>Math.abs(a.doc.scrollLeft-b))||(b=Math.min(b,a.display.scroller.scrollWidth-a.display.scroller.clientWidth),a.doc.scrollLeft=b,L(a),a.display.scroller.scrollLeft!=b&&(a.display.scroller.scrollLeft=b),a.display.scrollbarH.scrollLeft!=b&&(a.display.scrollbarH.scrollLeft=b))}function Xb(b,c){var d=c.wheelDeltaX,f=c.wheelDeltaY;null==d&&c.detail&&c.axis==c.HORIZONTAL_AXIS&&(d=c.detail),null==f&&c.detail&&c.axis==c.VERTICAL_AXIS?f=c.detail:null==f&&(f=c.wheelDelta);var g=b.display,i=g.scroller;if(d&&i.scrollWidth>i.clientWidth||f&&i.scrollHeight>i.clientHeight){if(f&&p&&e)for(var j=c.target;j!=i;j=j.parentNode)if(j.lineObj){b.display.currentWheelTarget=j;break}if(d&&!a&&!h&&null!=Wb)return f&&Tb(b,Math.max(0,Math.min(i.scrollTop+f*Wb,i.scrollHeight-i.clientHeight))),Ub(b,Math.max(0,Math.min(i.scrollLeft+d*Wb,i.scrollWidth-i.clientWidth))),we(c),g.wheelStartX=null,void 0;if(f&&null!=Wb){var k=f*Wb,l=b.doc.scrollTop,m=l+g.wrapper.clientHeight;0>k?l=Math.max(0,l+k-50):m=Math.min(b.doc.height,m+k+50),P(b,[],{top:l,bottom:m})}20>Vb&&(null==g.wheelStartX?(g.wheelStartX=i.scrollLeft,g.wheelStartY=i.scrollTop,g.wheelDX=d,g.wheelDY=f,setTimeout(function(){if(null!=g.wheelStartX){var a=i.scrollLeft-g.wheelStartX,b=i.scrollTop-g.wheelStartY,c=b&&g.wheelDY&&b/g.wheelDY||a&&g.wheelDX&&a/g.wheelDX;g.wheelStartX=g.wheelStartY=null,c&&(Wb=(Wb*Vb+c)/(Vb+1),++Vb)}},200)):(g.wheelDX+=d,g.wheelDY+=f))}}function Yb(a,b,c){if("string"==typeof b&&(b=ad[b],!b))return!1;a.display.pollingFast&&Gb(a)&&(a.display.pollingFast=!1);var d=a.doc,e=d.sel.shift,f=!1;try{Jb(a)&&(a.state.suppressEdits=!0),c&&(d.sel.shift=!1),f=b(a)!=Ke}finally{d.sel.shift=e,a.state.suppressEdits=!1}return f}function Zb(a){var b=a.state.keyMaps.slice(0);return a.options.extraKeys&&b.push(a.options.extraKeys),b.push(a.options.keyMap),b}function _b(a,b){var c=cd(a.options.keyMap),e=c.auto;clearTimeout($b),e&&!ed(b)&&($b=setTimeout(function(){cd(a.options.keyMap)==c&&(a.options.keyMap=e.call?e.call(null,a):e)},50));var f=fd(b,!0),g=!1;if(!f)return!1;var h=Zb(a);return g=b.shiftKey?dd("Shift-"+f,h,function(b){return Yb(a,b,!0)})||dd(f,h,function(b){return"string"==typeof b&&/^go[A-Z]/.test(b)?Yb(a,b):void 0}):dd(f,h,function(b){return Yb(a,b)}),"stop"==g&&(g=!1),g&&(we(b),$(a),d&&(b.oldKeyCode=b.keyCode,b.keyCode=0)),g}function ac(a,b,c){var d=dd("'"+c+"'",Zb(a),function(b){return Yb(a,b,!0)});return d&&(we(b),$(a)),d}function cc(a){var c=this;if(c.state.focused||ec(c),b&&27==a.keyCode&&(a.returnValue=!1),!c.options.onKeyEvent||!c.options.onKeyEvent(c,ve(a))){var d=a.keyCode;c.doc.sel.shift=16==d||a.shiftKey;var e=_b(c,a);h&&(bc=e?d:null,!e&&88==d&&!mf&&(p?a.metaKey:a.ctrlKey)&&c.replaceSelection(""))}}function dc(a){var c=this;if(!c.options.onKeyEvent||!c.options.onKeyEvent(c,ve(a))){var e=a.keyCode,f=a.charCode;if(h&&e==bc)return bc=null,we(a),void 0;if(!(h&&(!a.which||10>a.which)||j)||!_b(c,a)){var g=String.fromCharCode(null==f?e:f);this.options.electricChars&&this.doc.mode.electricChars&&this.options.smartIndent&&!Jb(this)&&this.doc.mode.electricChars.indexOf(g)>-1&&setTimeout(Ab(c,function(){Mc(c,c.doc.sel.to.line,"smart")}),75),ac(c,a,g)||(b&&!d&&(c.display.inputHasSelection=null),Fb(c))}}}function ec(a){"nocursor"!=a.options.readOnly&&(a.state.focused||(De(a,"focus",a),a.state.focused=!0,-1==a.display.wrapper.className.search(/\bCodeMirror-focused\b/)&&(a.display.wrapper.className+=" CodeMirror-focused"),Hb(a,!0)),Eb(a),$(a))}function fc(a){a.state.focused&&(De(a,"blur",a),a.state.focused=!1,a.display.wrapper.className=a.display.wrapper.className.replace(" CodeMirror-focused","")),clearInterval(a.display.blinker),setTimeout(function(){a.state.focused||(a.doc.sel.shift=!1)},150)}function hc(a,c){function k(){if(null!=e.input.selectionStart){var a=e.input.value=" "+(uc(f.from,f.to)?"":e.input.value);e.prevInput=" ",e.input.selectionStart=1,e.input.selectionEnd=a.length}}function l(){if(e.inputDiv.style.position="relative",e.input.style.cssText=j,d&&(e.scrollbarV.scrollTop=e.scroller.scrollTop=i),Eb(a),null!=e.input.selectionStart){(!b||d)&&k(),clearTimeout(gc);var c=0,f=function(){" "==e.prevInput&&0==e.input.selectionStart?Ab(a,ad.selectAll)(a):10>c++?gc=setTimeout(f,500):Hb(a)};gc=setTimeout(f,200)}}var e=a.display,f=a.doc.sel;if(!Lb(e,c)){var g=Mb(a,c),i=e.scroller.scrollTop;if(g&&!h){(uc(f.from,f.to)||vc(g,f.from)||!vc(g,f.to))&&Ab(a,Dc)(a.doc,g,g);var j=e.input.style.cssText;if(e.inputDiv.style.position="absolute",e.input.style.cssText="position: fixed; width: 30px; height: 30px; top: "+(c.clientY-5)+"px; left: "+(c.clientX-5)+"px; z-index: 1000; background: white; outline: none;"+"border-width: 0; outline: none; overflow: hidden; opacity: .05; -ms-opacity: .05; filter: alpha(opacity=5);",Ib(a),Hb(a,!0),uc(f.from,f.to)&&(e.input.value=e.prevInput=" "),b&&!d&&k(),t){ye(c);var m=function(){Ce(window,"mouseup",m),setTimeout(l,20)};Be(window,"mouseup",m)}else setTimeout(l,50)}}}function ic(a){return a.text?tc(a.from.line+a.text.length-1,Pe(a.text).length+(1==a.text.length?a.from.ch:0)):a.to}function jc(a,b,c){if(!vc(b.from,c))return yc(a,c);var d=b.text.length-1-(b.to.line-b.from.line);if(c.line>b.to.line+d){var e=c.line-d,f=a.first+a.size-1;return e>f?tc(f,ce(a,f).text.length):zc(c,ce(a,e).text.length)}if(c.line==b.to.line+d)return zc(c,Pe(b.text).length+(1==b.text.length?b.from.ch:0)+ce(a,b.to.line).text.length-b.to.ch);var g=c.line-b.from.line;return zc(c,b.text[g].length+(g?0:b.from.ch))}function kc(a,b,c){if(c&&"object"==typeof c)return{anchor:jc(a,b,c.anchor),head:jc(a,b,c.head)};if("start"==c)return{anchor:b.from,head:b.from};var d=ic(b);if("around"==c)return{anchor:b.from,head:d};if("end"==c)return{anchor:d,head:d};var e=function(a){if(vc(a,b.from))return a;if(!vc(b.to,a))return d;var c=a.line+b.text.length-(b.to.line-b.from.line)-1,e=a.ch;return a.line==b.to.line&&(e+=d.ch-b.to.ch),tc(c,e)};return{anchor:e(a.sel.anchor),head:e(a.sel.head)}}function lc(a,b,c){var d={canceled:!1,from:b.from,to:b.to,text:b.text,origin:b.origin,cancel:function(){this.canceled=!0}};return c&&(d.update=function(b,c,d,e){b&&(this.from=yc(a,b)),c&&(this.to=yc(a,c)),d&&(this.text=d),void 0!==e&&(this.origin=e)}),De(a,"beforeChange",a,d),a.cm&&De(a.cm,"beforeChange",a.cm,d),d.canceled?null:{from:d.from,to:d.to,text:d.text,origin:d.origin}}function mc(a,b,c,d){if(a.cm){if(!a.cm.curOp)return Ab(a.cm,mc)(a,b,c,d);if(a.cm.state.suppressEdits)return}if(!(Ie(a,"beforeChange")||a.cm&&Ie(a.cm,"beforeChange"))||(b=lc(a,b,!0))){var e=u&&!d&&sd(a,b.from,b.to);if(e){for(var f=e.length-1;f>=1;--f)nc(a,{from:e[f].from,to:e[f].to,text:[""]});e.length&&nc(a,{from:e[0].from,to:e[0].to,text:b.text},c)}else nc(a,b,c)}}function nc(a,b,c){var d=kc(a,b,c);ne(a,b,d,a.cm?a.cm.curOp.id:0/0),qc(a,b,d,qd(a,b));var e=[];ae(a,function(a,c){c||-1!=Re(e,a.history)||(te(a.history,b),e.push(a.history)),qc(a,b,null,qd(a,b))})}function oc(a,b){if(!a.cm||!a.cm.state.suppressEdits){var c=a.history,d=("undo"==b?c.done:c.undone).pop();if(d){c.dirtyCounter+="undo"==b?-1:1;var e={changes:[],anchorBefore:d.anchorAfter,headBefore:d.headAfter,anchorAfter:d.anchorBefore,headAfter:d.headBefore};("undo"==b?c.undone:c.done).push(e);for(var f=Ie(a,"beforeChange")||a.cm&&Ie(a.cm,"beforeChange"),g=d.changes.length-1;g>=0;--g){var h=d.changes[g];if(h.origin=b,f&&!lc(a,h,!1))return("undo"==b?c.done:c.undone).length=0,void 0;e.changes.push(me(a,h));var i=g?kc(a,h,null):{anchor:d.anchorBefore,head:d.headBefore};qc(a,h,i,rd(a,h));var j=[];ae(a,function(a,b){b||-1!=Re(j,a.history)||(te(a.history,h),j.push(a.history)),qc(a,h,null,rd(a,h))})}}}}function pc(a,b){function c(a){return tc(a.line+b,a.ch)}a.first+=b,a.cm&&Db(a.cm,a.first,a.first,b),a.sel.head=c(a.sel.head),a.sel.anchor=c(a.sel.anchor),a.sel.from=c(a.sel.from),a.sel.to=c(a.sel.to)}function qc(a,b,c,d){if(a.cm&&!a.cm.curOp)return Ab(a.cm,qc)(a,b,c,d);if(b.to.line<a.first)return pc(a,b.text.length-1-(b.to.line-b.from.line)),void 0;if(!(b.from.line>a.lastLine())){if(b.from.line<a.first){var e=b.text.length-1-(a.first-b.from.line);pc(a,e),b={from:tc(a.first,0),to:tc(b.to.line+e,b.to.ch),text:[Pe(b.text)],origin:b.origin}}var f=a.lastLine();b.to.line>f&&(b={from:b.from,to:tc(f,ce(a,f).text.length),text:[b.text[0]],origin:b.origin}),b.removed=de(a,b.from,b.to),c||(c=kc(a,b,null)),a.cm?rc(a.cm,b,d,c):Vd(a,b,d,c)}}function rc(a,b,c,d){var e=a.doc,f=a.display,g=b.from,h=b.to,i=!1,j=g.line;a.options.lineWrapping||(j=ge(wd(e,ce(e,g.line))),e.iter(j,h.line+1,function(a){return a==f.maxLine?(i=!0,!0):void 0})),vc(e.sel.head,b.from)||vc(b.to,e.sel.head)||(a.curOp.cursorActivity=!0),Vd(e,b,c,d,A(a)),a.options.lineWrapping||(e.iter(j,g.line+b.text.length,function(a){var b=G(e,a);b>f.maxLineLength&&(f.maxLine=a,f.maxLineLength=b,f.maxLineChanged=!0,i=!1)}),i&&(a.curOp.updateMaxLine=!0)),e.frontier=Math.min(e.frontier,g.line),_(a,400);var k=b.text.length-(h.line-g.line)-1;if(Db(a,g.line,h.line+1,k),Ie(a,"change")){var l={from:g,to:h,text:b.text,removed:b.removed,origin:b.origin};if(a.curOp.textChanged){for(var m=a.curOp.textChanged;m.next;m=m.next);m.next=l}else a.curOp.textChanged=l}}function sc(a,b,c,d,e){if(d||(d=c),vc(d,c)){var f=d;d=c,c=f}"string"==typeof b&&(b=kf(b)),mc(a,{from:c,to:d,text:b,origin:e},null)}function tc(a,b){return this instanceof tc?(this.line=a,this.ch=b,void 0):new tc(a,b)}function uc(a,b){return a.line==b.line&&a.ch==b.ch}function vc(a,b){return a.line<b.line||a.line==b.line&&a.ch<b.ch}function wc(a){return tc(a.line,a.ch)}function xc(a,b){return Math.max(a.first,Math.min(b,a.first+a.size-1))}function yc(a,b){if(b.line<a.first)return tc(a.first,0);var c=a.first+a.size-1;return b.line>c?tc(c,ce(a,c).text.length):zc(b,ce(a,b.line).text.length)}function zc(a,b){var c=a.ch;return null==c||c>b?tc(a.line,b):0>c?tc(a.line,0):a}function Ac(a,b){return b>=a.first&&a.first+a.size>b}function Bc(a,b,c,d){if(a.sel.shift||a.sel.extend){var e=a.sel.anchor;if(c){var f=vc(b,e);f!=vc(c,e)?(e=b,b=c):f!=vc(b,c)&&(b=c)}Dc(a,e,b,d)}else Dc(a,b,c||b,d);a.cm&&(a.cm.curOp.userSelChange=!0)}function Cc(a,b,c){var d={anchor:b,head:c};return De(a,"beforeSelectionChange",a,d),a.cm&&De(a.cm,"beforeSelectionChange",a.cm,d),d.anchor=yc(a,d.anchor),d.head=yc(a,d.head),d}function Dc(a,b,c,d,e){if(!e&&Ie(a,"beforeSelectionChange")||a.cm&&Ie(a.cm,"beforeSelectionChange")){var f=Cc(a,b,c);c=f.head,b=f.anchor}var g=a.sel;if(g.goalColumn=null,(e||!uc(b,g.anchor))&&(b=Fc(a,b,d,"push"!=e)),(e||!uc(c,g.head))&&(c=Fc(a,c,d,"push"!=e)),!uc(g.anchor,b)||!uc(g.head,c)){g.anchor=b,g.head=c;var h=vc(c,b);g.from=h?c:b,g.to=h?b:c,a.cm&&(a.cm.curOp.updateInput=a.cm.curOp.selectionChanged=a.cm.curOp.cursorActivity=!0),Ge(a,"cursorActivity",a)}}function Ec(a){Dc(a.doc,a.doc.sel.from,a.doc.sel.to,null,"push")}function Fc(a,b,c,d){var e=!1,f=b,g=c||1;a.cantEdit=!1;a:for(;;){var h=ce(a,f.line);if(h.markedSpans)for(var i=0;h.markedSpans.length>i;++i){var j=h.markedSpans[i],k=j.marker;if((null==j.from||(k.inclusiveLeft?j.from<=f.ch:j.from<f.ch))&&(null==j.to||(k.inclusiveRight?j.to>=f.ch:j.to>f.ch))){if(d&&(De(k,"beforeCursorEnter"),k.explicitlyCleared)){if(h.markedSpans){--i;continue}break}if(!k.atomic)continue;var l=k.find()[0>g?"from":"to"];if(uc(l,f)&&(l.ch+=g,0>l.ch?l=l.line>a.first?yc(a,tc(l.line-1)):null:l.ch>h.text.length&&(l=l.line<a.first+a.size-1?tc(l.line+1,0):null),!l)){if(e)return d?(a.cantEdit=!0,tc(a.first,0)):Fc(a,b,c,!0);e=!0,l=b,g=-g}f=l;continue a}}return f}}function Gc(a){var b=Hc(a,a.doc.sel.head,a.options.cursorScrollMargin);if(a.state.focused){var c=a.display,d=cf(c.sizer),e=null,f=db(a.display);if(0>b.top+f+d.top?e=!0:b.bottom+f+d.top>(window.innerHeight||document.documentElement.clientHeight)&&(e=!1),null!=e&&!m){var g="none"==c.cursor.style.display;g&&(c.cursor.style.display="",c.cursor.style.left=b.left+"px",c.cursor.style.top=b.top-c.viewOffset+"px"),c.cursor.scrollIntoView(e),g&&(c.cursor.style.display="none")}}}function Hc(a,b,c){for(null==c&&(c=0);;){var d=!1,e=qb(a,b),f=Jc(a,e.left,e.top-c,e.left,e.bottom+c),g=a.doc.scrollTop,h=a.doc.scrollLeft;if(null!=f.scrollTop&&(Tb(a,f.scrollTop),Math.abs(a.doc.scrollTop-g)>1&&(d=!0)),null!=f.scrollLeft&&(Ub(a,f.scrollLeft),Math.abs(a.doc.scrollLeft-h)>1&&(d=!0)),!d)return e}}function Ic(a,b,c,d,e){var f=Jc(a,b,c,d,e);null!=f.scrollTop&&Tb(a,f.scrollTop),null!=f.scrollLeft&&Ub(a,f.scrollLeft)}function Jc(a,b,c,d,e){var f=a.display,g=db(f);c+=g,e+=g,0>c&&(c=0);var h=f.scroller.clientHeight-Je,i=f.scroller.scrollTop,j={},k=a.doc.height+eb(f),l=g+10>c,m=e+g>k-10;if(i>c)j.scrollTop=l?0:c;else if(e>i+h){var n=Math.min(c,(m?k:e)-h);n!=i&&(j.scrollTop=n)}var o=f.scroller.clientWidth-Je,p=f.scroller.scrollLeft;b+=f.gutters.offsetWidth,d+=f.gutters.offsetWidth;var q=f.gutters.offsetWidth,r=q+10>b;return p+q>b||r?(r&&(b=0),j.scrollLeft=Math.max(0,b-10-q)):d>o+p-3&&(j.scrollLeft=d+10-o),j}function Kc(a,b,c){a.curOp.updateScrollPos={scrollLeft:null==b?a.doc.scrollLeft:b,scrollTop:null==c?a.doc.scrollTop:c}}function Lc(a,b,c){var d=a.curOp.updateScrollPos||(a.curOp.updateScrollPos={scrollLeft:a.doc.scrollLeft,scrollTop:a.doc.scrollTop}),e=a.display.scroller;d.scrollTop=Math.max(0,Math.min(e.scrollHeight-e.clientHeight,d.scrollTop+c)),d.scrollLeft=Math.max(0,Math.min(e.scrollWidth-e.clientWidth,d.scrollLeft+b))}function Mc(a,b,c,d){var e=a.doc;if(c||(c="add"),"smart"==c)if(a.doc.mode.indent)var f=cb(a,b);else c="prev";var k,g=a.options.tabSize,h=ce(e,b),i=Me(h.text,null,g),j=h.text.match(/^\s*/)[0];if("smart"==c&&(k=a.doc.mode.indent(f,h.text.slice(j.length),h.text),k==Ke)){if(!d)return;c="prev"}"prev"==c?k=b>e.first?Me(ce(e,b-1).text,null,g):0:"add"==c?k=i+a.options.indentUnit:"subtract"==c&&(k=i-a.options.indentUnit),k=Math.max(0,k);var l="",m=0;if(a.options.indentWithTabs)for(var n=Math.floor(k/g);n;--n)m+=g,l+="	";k>m&&(l+=Oe(k-m)),l!=j&&sc(a.doc,l,tc(b,0),tc(b,j.length),"+input"),h.stateAfter=null}function Nc(a,b,c){var d=b,e=b,f=a.doc;return"number"==typeof b?e=ce(f,xc(f,b)):d=ge(b),null==d?null:c(e,d)?(Db(a,d,d+1),e):null}function Oc(a,b,c,d,e){function k(){var b=f+c;return a.first>b||b>=a.first+a.size?j=!1:(f=b,i=ce(a,b))}function l(a){var b=(e?zf:Af)(i,g,c,!0);if(null==b){if(a||!k())return j=!1;g=e?(0>c?sf:rf)(i):0>c?i.text.length:0}else g=b;return!0}var f=b.line,g=b.ch,h=c,i=ce(a,f),j=!0;if("char"==d)l();else if("column"==d)l(!0);else if("word"==d||"group"==d)for(var m=null,n="group"==d,o=!0;!(0>c)||l(!o);o=!1){var p=i.text.charAt(g)||"\n",q=Xe(p)?"w":n?/\s/.test(p)?null:"p":null;if(m&&m!=q){0>c&&(c=1,l());break}if(q&&(m=q),c>0&&!l(!o))break}var r=Fc(a,tc(f,g),h,!0);return j||(r.hitSide=!0),r}function Pc(a,b,c,d){var g,e=a.doc,f=b.left;if("page"==d){var h=Math.min(a.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight);g=b.top+c*(h-(0>c?1.5:.5)*vb(a.display))}else"line"==d&&(g=c>0?b.bottom+3:b.top-3);for(;;){var i=sb(a,f,g);if(!i.outside)break;if(0>c?0>=g:g>=e.height){i.hitSide=!0;break}g+=5*c}return i}function Qc(a,b){var c=b.ch,d=b.ch;if(a){b.after===!1||d==a.length?--c:++d;for(var e=a.charAt(c),f=Xe(e)?Xe:/\s/.test(e)?function(a){return/\s/.test(a)}:function(a){return!/\s/.test(a)&&!Xe(a)};c>0&&f(a.charAt(c-1));)--c;for(;a.length>d&&f(a.charAt(d));)++d}return{from:tc(b.line,c),to:tc(b.line,d)}}function Rc(a,b){Bc(a.doc,tc(b,0),yc(a.doc,tc(b+1,0)))}function Uc(a,b,c,d){w.defaults[a]=b,c&&(Sc[a]=d?function(a,b,d){d!=Vc&&c(a,b,d)}:c)}function $c(a,b){if(b===!0)return b;if(a.copyState)return a.copyState(b);var c={};for(var d in b){var e=b[d];e instanceof Array&&(e=e.concat([])),c[d]=e}return c}function _c(a,b,c){return a.startState?a.startState(b,c):!0}function cd(a){return"string"==typeof a?bd[a]:a}function dd(a,b,c){function d(b){b=cd(b);var e=b[a];if(e===!1)return"stop";if(null!=e&&c(e))return!0;if(b.nofallthrough)return"stop";var f=b.fallthrough;if(null==f)return!1;if("[object Array]"!=Object.prototype.toString.call(f))return d(f);for(var g=0,h=f.length;h>g;++g){var i=d(f[g]);if(i)return i}return!1}for(var e=0;b.length>e;++e){var f=d(b[e]);if(f)return f}}function ed(a){var b=nf[a.keyCode];return"Ctrl"==b||"Alt"==b||"Shift"==b||"Mod"==b}function fd(a,b){if(h&&34==a.keyCode&&a["char"])return!1;var c=nf[a.keyCode];return null==c||a.altGraphKey?!1:(a.altKey&&(c="Alt-"+c),(s?a.metaKey:a.ctrlKey)&&(c="Ctrl-"+c),(s?a.ctrlKey:a.metaKey)&&(c="Cmd-"+c),!b&&a.shiftKey&&(c="Shift-"+c),c)}function gd(a,b){this.pos=this.start=0,this.string=a,this.tabSize=b||8,this.lastColumnPos=this.lastColumnValue=0}function hd(a,b){this.lines=[],this.type=b,this.doc=a}function id(a,b,c,d,e){if(d&&d.shared)return kd(a,b,c,d,e);if(a.cm&&!a.cm.curOp)return Ab(a.cm,id)(a,b,c,d,e);var f=new hd(a,e);if("range"==e&&!vc(b,c))return f;d&&Te(d,f),f.replacedWith&&(f.collapsed=!0,f.replacedWith=$e("span",[f.replacedWith],"CodeMirror-widget")),f.collapsed&&(v=!0),f.addToHistory&&ne(a,{from:b,to:c,origin:"markText"},{head:a.sel.head,anchor:a.sel.anchor},0/0);var i,j,l,g=b.line,h=0,k=a.cm;if(a.iter(g,c.line+1,function(d){k&&f.collapsed&&!k.options.lineWrapping&&wd(a,d)==k.display.maxLine&&(l=!0);var e={from:null,to:null,marker:f};h+=d.text.length,g==b.line&&(e.from=b.ch,h-=b.ch),g==c.line&&(e.to=c.ch,h-=d.text.length-c.ch),f.collapsed&&(g==c.line&&(j=td(d,c.ch)),g==b.line?i=td(d,b.ch):fe(d,0)),nd(d,e),++g}),f.collapsed&&a.iter(b.line,c.line+1,function(b){xd(a,b)&&fe(b,0)}),f.clearOnEnter&&Be(f,"beforeCursorEnter",function(){f.clear()}),f.readOnly&&(u=!0,(a.history.done.length||a.history.undone.length)&&a.clearHistory()),f.collapsed){if(i!=j)throw Error("Inserting collapsed marker overlapping an existing one");f.size=h,f.atomic=!0}return k&&(l&&(k.curOp.updateMaxLine=!0),(f.className||f.startStyle||f.endStyle||f.collapsed)&&Db(k,b.line,c.line+1),f.atomic&&Ec(k)),f}function jd(a,b){this.markers=a,this.primary=b;for(var c=0,d=this;a.length>c;++c)a[c].parent=this,Be(a[c],"clear",function(){d.clear()})}function kd(a,b,c,d,e){d=Te(d),d.shared=!1;var f=[id(a,b,c,d,e)],g=f[0],h=d.replacedWith;return ae(a,function(a){h&&(d.replacedWith=h.cloneNode(!0)),f.push(id(a,yc(a,b),yc(a,c),d,e));for(var i=0;a.linked.length>i;++i)if(a.linked[i].isParent)return;g=Pe(f)}),new jd(f,g)}function ld(a,b){if(a)for(var c=0;a.length>c;++c){var d=a[c];if(d.marker==b)return d}}function md(a,b){for(var c,d=0;a.length>d;++d)a[d]!=b&&(c||(c=[])).push(a[d]);return c}function nd(a,b){a.markedSpans=a.markedSpans?a.markedSpans.concat([b]):[b],b.marker.attachLine(a)}function od(a,b,c){if(a)for(var e,d=0;a.length>d;++d){var f=a[d],g=f.marker,h=null==f.from||(g.inclusiveLeft?b>=f.from:b>f.from);if(h||"bookmark"==g.type&&f.from==b&&(!c||!f.marker.insertLeft)){var i=null==f.to||(g.inclusiveRight?f.to>=b:f.to>b);(e||(e=[])).push({from:f.from,to:i?null:f.to,marker:g})}}return e}function pd(a,b,c){if(a)for(var e,d=0;a.length>d;++d){var f=a[d],g=f.marker,h=null==f.to||(g.inclusiveRight?f.to>=b:f.to>b);if(h||"bookmark"==g.type&&f.from==b&&(!c||f.marker.insertLeft)){var i=null==f.from||(g.inclusiveLeft?b>=f.from:b>f.from);(e||(e=[])).push({from:i?null:f.from-b,to:null==f.to?null:f.to-b,marker:g})}}return e}function qd(a,b){var c=Ac(a,b.from.line)&&ce(a,b.from.line).markedSpans,d=Ac(a,b.to.line)&&ce(a,b.to.line).markedSpans;if(!c&&!d)return null;var e=b.from.ch,f=b.to.ch,g=uc(b.from,b.to),h=od(c,e,g),i=pd(d,f,g),j=1==b.text.length,k=Pe(b.text).length+(j?e:0);if(h)for(var l=0;h.length>l;++l){var m=h[l];if(null==m.to){var n=ld(i,m.marker);n?j&&(m.to=null==n.to?null:n.to+k):m.to=e}}if(i)for(var l=0;i.length>l;++l){var m=i[l];if(null!=m.to&&(m.to+=k),null==m.from){var n=ld(h,m.marker);n||(m.from=k,j&&(h||(h=[])).push(m))}else m.from+=k,j&&(h||(h=[])).push(m)}var o=[h];if(!j){var q,p=b.text.length-2;if(p>0&&h)for(var l=0;h.length>l;++l)null==h[l].to&&(q||(q=[])).push({from:null,to:null,marker:h[l].marker});for(var l=0;p>l;++l)o.push(q);o.push(i)}return o}function rd(a,b){var c=pe(a,b),d=qd(a,b);if(!c)return d;if(!d)return c;for(var e=0;c.length>e;++e){var f=c[e],g=d[e];if(f&&g)a:for(var h=0;g.length>h;++h){for(var i=g[h],j=0;f.length>j;++j)if(f[j].marker==i.marker)continue a;f.push(i)}else g&&(c[e]=g)}return c}function sd(a,b,c){var d=null;if(a.iter(b.line,c.line+1,function(a){if(a.markedSpans)for(var b=0;a.markedSpans.length>b;++b){var c=a.markedSpans[b].marker;!c.readOnly||d&&-1!=Re(d,c)||(d||(d=[])).push(c)}}),!d)return null;for(var e=[{from:b,to:c}],f=0;d.length>f;++f)for(var g=d[f],h=g.find(),i=0;e.length>i;++i){var j=e[i];if(!vc(j.to,h.from)&&!vc(h.to,j.from)){var k=[i,1];(vc(j.from,h.from)||!g.inclusiveLeft&&uc(j.from,h.from))&&k.push({from:j.from,to:h.from}),(vc(h.to,j.to)||!g.inclusiveRight&&uc(j.to,h.to))&&k.push({from:h.to,to:j.to}),e.splice.apply(e,k),i+=k.length-1}}return e}function td(a,b){var d,c=v&&a.markedSpans;if(c)for(var e,f=0;c.length>f;++f)e=c[f],e.marker.collapsed&&(null==e.from||b>e.from)&&(null==e.to||e.to>b)&&(!d||d.width<e.marker.width)&&(d=e.marker);return d}function ud(a){return td(a,-1)}function vd(a){return td(a,a.text.length+1)}function wd(a,b){for(var c;c=ud(b);)b=ce(a,c.find().from.line);return b}function xd(a,b){var c=v&&b.markedSpans;if(c)for(var d,e=0;c.length>e;++e)if(d=c[e],d.marker.collapsed){if(null==d.from)return!0;if(0==d.from&&d.marker.inclusiveLeft&&yd(a,b,d))return!0}}function yd(a,b,c){if(null==c.to){var d=c.marker.find().to,e=ce(a,d.line);return yd(a,e,ld(e.markedSpans,c.marker))}if(c.marker.inclusiveRight&&c.to==b.text.length)return!0;for(var f,g=0;b.markedSpans.length>g;++g)if(f=b.markedSpans[g],f.marker.collapsed&&f.from==c.to&&(f.marker.inclusiveLeft||c.marker.inclusiveRight)&&yd(a,b,f))return!0}function zd(a){var b=a.markedSpans;if(b){for(var c=0;b.length>c;++c)b[c].marker.detachLine(a);a.markedSpans=null}}function Ad(a,b){if(b){for(var c=0;b.length>c;++c)b[c].marker.attachLine(a);a.markedSpans=b}}function Cd(a){return function(){var b=!this.cm.curOp;b&&yb(this.cm);try{var c=a.apply(this,arguments)}finally{b&&zb(this.cm)}return c}}function Dd(a){return null!=a.height?a.height:(a.node.parentNode&&1==a.node.parentNode.nodeType||af(a.cm.display.measure,$e("div",[a.node],null,"position: relative")),a.height=a.node.offsetHeight)}function Ed(a,b,c,d){var e=new Bd(a,c,d);return e.noHScroll&&(a.display.alignWidgets=!0),Nc(a,b,function(b){if((b.widgets||(b.widgets=[])).push(e),e.line=b,!xd(a.doc,b)||e.showIfHidden){var c=ie(a,b)<a.display.scroller.scrollTop;fe(b,b.height+Dd(e)),c&&Lc(a,0,e.height)}return!0}),e}function Fd(a,b,c){var d={text:a};return Ad(d,b),d.height=c?c(d):1,d}function Gd(a,b,c,d){a.text=b,a.stateAfter&&(a.stateAfter=null),a.styles&&(a.styles=null),null!=a.order&&(a.order=null),zd(a),Ad(a,c);var e=d?d(a):1;e!=a.height&&fe(a,e)}function Hd(a){a.parent=null,zd(a)}function Id(a,b,c,d,e){var f=c.flattenSpans;null==f&&(f=a.options.flattenSpans);var j,g="",h=null,i=new gd(b,a.options.tabSize);for(""==b&&c.blankLine&&c.blankLine(d);!i.eol();){i.pos>a.options.maxHighlightLength?(f=!1,i.pos=Math.min(b.length,i.start+5e4),j=null):j=c.token(i,d);var k=i.current();i.start=i.pos,f&&h==j?g+=k:(g&&e(g,h),g=k,h=j)}g&&e(g,h)}function Jd(a,b,c){var d=[a.state.modeGen];Id(a,b.text,a.doc.mode,c,function(a,b){d.push(a,b)});for(var e=0;a.state.overlays.length>e;++e){var f=a.state.overlays[e],g=1;Id(a,b.text,f.mode,!0,function(a,b){for(var c=g,e=a.length;e;){var h=d[g],i=h.length;e>=i?e-=i:(d.splice(g,1,h.slice(0,e),d[g+1],h.slice(e)),e=0),g+=2}if(b)if(f.opaque)d.splice(c,g-c,a,b),g=c+2;else for(;g>c;c+=2){var h=d[c+1];d[c+1]=h?h+" "+b:b}})}return d}function Kd(a,b){return b.styles&&b.styles[0]==a.state.modeGen||(b.styles=Jd(a,b,b.stateAfter=cb(a,ge(b)))),b.styles}function Ld(a,b,c){var d=a.doc.mode,e=new gd(b.text,a.options.tabSize);for(""==b.text&&d.blankLine&&d.blankLine(c);!e.eol()&&e.pos<=a.options.maxHighlightLength;)d.token(e,c),e.start=e.pos}function Nd(a){return a?Md[a]||(Md[a]="cm-"+a.replace(/ +/g," cm-")):null}function Od(a,c,d){for(var f,h,i,g=c,j=!0;f=ud(g);)j=!1,g=ce(a.doc,f.find().from.line),h||(h=g);var k={pre:$e("pre"),col:0,pos:0,display:!d,measure:null,addedOne:!1,cm:a};g.textClass&&(k.pre.className=g.textClass);do{k.measure=g==c&&d,k.pos=0,k.addToken=k.measure?Rd:Qd,(b||e)&&a.getOption("lineWrapping")&&(k.addToken=Sd(k.addToken)),d&&i&&g!=c&&!k.addedOne&&(d[0]=k.pre.appendChild(jf(a.display.measure)),k.addedOne=!0);var l=Ud(g,k,Kd(a,g));i=g==h,l&&(g=ce(a.doc,l.to.line),j=!1)}while(l);d&&!k.addedOne&&(d[0]=k.pre.appendChild(j?$e("span","\u00a0"):jf(a.display.measure))),k.pre.firstChild||xd(a.doc,c)||k.pre.appendChild(document.createTextNode("\u00a0"));var m;if(d&&b&&(m=je(g))){var n=m.length-1;m[n].from==m[n].to&&--n;var o=m[n],p=m[n-1];if(o.from+1==o.to&&p&&o.level<p.level){var q=d[k.pos-1];q&&q.parentNode.insertBefore(q.measureRight=jf(a.display.measure),q.nextSibling)}}return De(a,"renderLine",a,c,k.pre),k.pre}function Qd(a,b,c,d,e){if(b){if(Pd.test(b))for(var f=document.createDocumentFragment(),g=0;;){Pd.lastIndex=g;var h=Pd.exec(b),i=h?h.index-g:b.length-g;if(i&&(f.appendChild(document.createTextNode(b.slice(g,g+i))),a.col+=i),!h)break;if(g+=i+1,"	"==h[0]){var j=a.cm.options.tabSize,k=j-a.col%j;f.appendChild($e("span",Oe(k),"cm-tab")),a.col+=k}else{var l=$e("span","\u2022","cm-invalidchar");l.title="\\u"+h[0].charCodeAt(0).toString(16),f.appendChild(l),a.col+=1}}else{a.col+=b.length;var f=document.createTextNode(b)}if(c||d||e||a.measure){var m=c||"";return d&&(m+=d),e&&(m+=e),a.pre.appendChild($e("span",[f],m))}a.pre.appendChild(f)}}function Rd(a,c,d,e,f){for(var g=a.cm.options.lineWrapping,h=0;c.length>h;++h){var i=c.charAt(h),j=0==h;i>="\ud800"&&"\udbff">i&&c.length-1>h?(i=c.slice(h,h+2),++h):h&&g&&ef(c,h)&&a.pre.appendChild($e("wbr"));var k=a.measure[a.pos]=Qd(a,i,d,j&&e,h==c.length-1&&f);b&&g&&" "==i&&h&&!/\s/.test(c.charAt(h-1))&&c.length-1>h&&!/\s/.test(c.charAt(h+1))&&(k.style.whiteSpace="normal"),a.pos+=i.length}c.length&&(a.addedOne=!0)}function Sd(a){function b(a){for(var b=" ",c=0;a.length-2>c;++c)b+=c%2?" ":"\u00a0";return b+=" "}return function(c,d,e,f,g){return a(c,d.replace(/ {3,}/,b),e,f,g)}}function Td(a,b,c){c&&(a.display||(c=c.cloneNode(!0)),a.pre.appendChild(c),a.measure&&b&&(a.measure[a.pos]=c,a.addedOne=!0)),a.pos+=b}function Ud(a,b,c){var d=a.markedSpans;if(d)for(var j,l,m,n,o,f=a.text,g=f.length,h=0,e=1,i="",k=0;;){if(k==h){l=m=n="",o=null,k=1/0;for(var p=null,q=0;d.length>q;++q){var r=d[q],s=r.marker;h>=r.from&&(null==r.to||r.to>h)?(null!=r.to&&k>r.to&&(k=r.to,m=""),s.className&&(l+=" "+s.className),s.startStyle&&r.from==h&&(n+=" "+s.startStyle),s.endStyle&&r.to==k&&(m+=" "+s.endStyle),s.collapsed&&(!o||o.marker.width<s.width)&&(o=r)):r.from>h&&k>r.from&&(k=r.from),"bookmark"==s.type&&r.from==h&&s.replacedWith&&(p=s.replacedWith)}if(o&&(o.from||0)==h&&(Td(b,(null==o.to?g:o.to)-h,null!=o.from&&o.marker.replacedWith),null==o.to))return o.marker.find();p&&!o&&Td(b,0,p)}if(h>=g)break;for(var t=Math.min(g,k);;){if(i){var u=h+i.length;if(!o){var v=u>t?i.slice(0,t-h):i;b.addToken(b,v,j?j+l:l,n,h+v.length==k?m:"")}if(u>=t){i=i.slice(t-h),h=t;break}h=u,n=""}i=c[e++],j=Nd(c[e++])}}else for(var e=1;c.length>e;e+=2)b.addToken(b,c[e],Nd(c[e+1]))}function Vd(a,b,c,d,e){function f(a){return c?c[a]:null}function g(a,c,d){Gd(a,c,d,e),Ge(a,"change",a,b)}var h=b.from,i=b.to,j=b.text,k=ce(a,h.line),l=ce(a,i.line),m=Pe(j),n=f(j.length-1),o=i.line-h.line;if(0==h.ch&&0==i.ch&&""==m){for(var p=0,q=j.length-1,r=[];q>p;++p)r.push(Fd(j[p],f(p),e));g(l,l.text,n),o&&a.remove(h.line,o),r.length&&a.insert(h.line,r)}else if(k==l)if(1==j.length)g(k,k.text.slice(0,h.ch)+m+k.text.slice(i.ch),n);else{for(var r=[],p=1,q=j.length-1;q>p;++p)r.push(Fd(j[p],f(p),e));r.push(Fd(m+k.text.slice(i.ch),n,e)),g(k,k.text.slice(0,h.ch)+j[0],f(0)),a.insert(h.line+1,r)}else if(1==j.length)g(k,k.text.slice(0,h.ch)+j[0]+l.text.slice(i.ch),f(0)),a.remove(h.line+1,o);else{g(k,k.text.slice(0,h.ch)+j[0],f(0)),g(l,m+l.text.slice(i.ch),n);for(var p=1,q=j.length-1,r=[];q>p;++p)r.push(Fd(j[p],f(p),e));o>1&&a.remove(h.line+1,o-1),a.insert(h.line+1,r)}Ge(a,"change",a,b),Dc(a,d.anchor,d.head,null,!0)}function Wd(a){this.lines=a,this.parent=null;for(var b=0,c=a.length,d=0;c>b;++b)a[b].parent=this,d+=a[b].height;this.height=d}function Xd(a){this.children=a;for(var b=0,c=0,d=0,e=a.length;e>d;++d){var f=a[d];b+=f.chunkSize(),c+=f.height,f.parent=this}this.size=b,this.height=c,this.parent=null}function ae(a,b,c){function d(a,e,f){if(a.linked)for(var g=0;a.linked.length>g;++g){var h=a.linked[g];if(h.doc!=e){var i=f&&h.sharedHist;(!c||i)&&(b(h.doc,i),d(h.doc,a,i))}}}d(a,null,!0)}function be(a,b){if(b.cm)throw Error("This document is already in use.");a.doc=b,b.cm=a,B(a),y(a),a.options.lineWrapping||H(a),a.options.mode=b.modeOption,Db(a)}function ce(a,b){for(b-=a.first;!a.lines;)for(var c=0;;++c){var d=a.children[c],e=d.chunkSize();if(e>b){a=d;break}b-=e}return a.lines[b]}function de(a,b,c){var d=[],e=b.line;return a.iter(b.line,c.line+1,function(a){var f=a.text;e==c.line&&(f=f.slice(0,c.ch)),e==b.line&&(f=f.slice(b.ch)),d.push(f),++e}),d}function ee(a,b,c){var d=[];return a.iter(b,c,function(a){d.push(a.text)}),d}function fe(a,b){for(var c=b-a.height,d=a;d;d=d.parent)d.height+=c}function ge(a){if(null==a.parent)return null;for(var b=a.parent,c=Re(b.lines,a),d=b.parent;d;b=d,d=d.parent)for(var e=0;d.children[e]!=b;++e)c+=d.children[e].chunkSize();return c+b.first}function he(a,b){var c=a.first;a:do{for(var d=0,e=a.children.length;e>d;++d){var f=a.children[d],g=f.height;if(g>b){a=f;continue a}b-=g,c+=f.chunkSize()}return c}while(!a.lines);for(var d=0,e=a.lines.length;e>d;++d){var h=a.lines[d],i=h.height;if(i>b)break;b-=i}return c+d}function ie(a,b){b=wd(a.doc,b);for(var c=0,d=b.parent,e=0;d.lines.length>e;++e){var f=d.lines[e];if(f==b)break;c+=f.height}for(var g=d.parent;g;d=g,g=d.parent)for(var e=0;g.children.length>e;++e){var h=g.children[e];if(h==d)break;c+=h.height}return c}function je(a){var b=a.order;return null==b&&(b=a.order=Bf(a.text)),b}function ke(){return{done:[],undone:[],undoDepth:1/0,lastTime:0,lastOp:null,lastOrigin:null,dirtyCounter:0}}function le(a,b,c,d){var e=b["spans_"+a.id],f=0;a.iter(Math.max(a.first,c),Math.min(a.first+a.size,d),function(c){c.markedSpans&&((e||(e=b["spans_"+a.id]={}))[f]=c.markedSpans),++f})}function me(a,b){var c={from:b.from,to:ic(b),text:de(a,b.from,b.to)};return le(a,c,b.from.line,b.to.line+1),ae(a,function(a){le(a,c,b.from.line,b.to.line+1)},!0),c}function ne(a,b,c,d){var e=a.history;e.undone.length=0;var f=+new Date,g=Pe(e.done);if(g&&(e.lastOp==d||e.lastOrigin==b.origin&&b.origin&&("+"==b.origin.charAt(0)&&a.cm&&e.lastTime>f-a.cm.options.historyEventDelay||"*"==b.origin.charAt(0)))){var h=Pe(g.changes);
uc(b.from,b.to)&&uc(b.from,h.to)?h.to=ic(b):g.changes.push(me(a,b)),g.anchorAfter=c.anchor,g.headAfter=c.head}else{for(g={changes:[me(a,b)],anchorBefore:a.sel.anchor,headBefore:a.sel.head,anchorAfter:c.anchor,headAfter:c.head},e.done.push(g);e.done.length>e.undoDepth;)e.done.shift();0>e.dirtyCounter?e.dirtyCounter=0/0:e.dirtyCounter++}e.lastTime=f,e.lastOp=d,e.lastOrigin=b.origin}function oe(a){if(!a)return null;for(var c,b=0;a.length>b;++b)a[b].marker.explicitlyCleared?c||(c=a.slice(0,b)):c&&c.push(a[b]);return c?c.length?c:null:a}function pe(a,b){var c=b["spans_"+a.id];if(!c)return null;for(var d=0,e=[];b.text.length>d;++d)e.push(oe(c[d]));return e}function qe(a,b){for(var c=0,d=[];a.length>c;++c){var e=a[c],f=e.changes,g=[];d.push({changes:g,anchorBefore:e.anchorBefore,headBefore:e.headBefore,anchorAfter:e.anchorAfter,headAfter:e.headAfter});for(var h=0;f.length>h;++h){var j,i=f[h];if(g.push({from:i.from,to:i.to,text:i.text}),b)for(var k in i)(j=k.match(/^spans_(\d+)$/))&&Re(b,Number(j[1]))>-1&&(Pe(g)[k]=i[k],delete i[k])}}return d}function re(a,b,c,d){a.line>c?a.line+=d:a.line>b&&(a.line=b,a.ch=0)}function se(a,b,c,d){for(var e=0;a.length>e;++e){for(var f=a[e],g=!0,h=0;f.changes.length>h;++h){var i=f.changes[h];if(f.copied||(i.from=wc(i.from),i.to=wc(i.to)),i.from.line>c)i.from.line+=d,i.to.line+=d;else if(i.to.line>=b){g=!1;break}}f.copied||(f.anchorBefore=wc(f.anchorBefore),f.headBefore=wc(f.headBefore),f.anchorAfter=wc(f.anchorAfter),f.readAfter=wc(f.headAfter),f.copied=!0),g?(re(f.anchorBefore),re(f.headBefore),re(f.anchorAfter),re(f.headAfter)):(a.splice(0,e+1),e=0)}}function te(a,b){var c=b.from.line,d=b.to.line,e=b.text.length-(d-c)-1;se(a.done,c,d,e),se(a.undone,c,d,e)}function ue(){ye(this)}function ve(a){return a.stop||(a.stop=ue),a}function we(a){a.preventDefault?a.preventDefault():a.returnValue=!1}function xe(a){a.stopPropagation?a.stopPropagation():a.cancelBubble=!0}function ye(a){we(a),xe(a)}function ze(a){return a.target||a.srcElement}function Ae(a){var b=a.which;return null==b&&(1&a.button?b=1:2&a.button?b=3:4&a.button&&(b=2)),p&&a.ctrlKey&&1==b&&(b=3),b}function Be(a,b,c){if(a.addEventListener)a.addEventListener(b,c,!1);else if(a.attachEvent)a.attachEvent("on"+b,c);else{var d=a._handlers||(a._handlers={}),e=d[b]||(d[b]=[]);e.push(c)}}function Ce(a,b,c){if(a.removeEventListener)a.removeEventListener(b,c,!1);else if(a.detachEvent)a.detachEvent("on"+b,c);else{var d=a._handlers&&a._handlers[b];if(!d)return;for(var e=0;d.length>e;++e)if(d[e]==c){d.splice(e,1);break}}}function De(a,b){var c=a._handlers&&a._handlers[b];if(c)for(var d=Array.prototype.slice.call(arguments,2),e=0;c.length>e;++e)c[e].apply(null,d)}function Ge(a,b){function e(a){return function(){a.apply(null,d)}}var c=a._handlers&&a._handlers[b];if(c){var d=Array.prototype.slice.call(arguments,2);Ee||(++Fe,Ee=[],setTimeout(He,0));for(var f=0;c.length>f;++f)Ee.push(e(c[f]))}}function He(){--Fe;var a=Ee;Ee=null;for(var b=0;a.length>b;++b)a[b]()}function Ie(a,b){var c=a._handlers&&a._handlers[b];return c&&c.length>0}function Le(){this.id=null}function Me(a,b,c,d,e){null==b&&(b=a.search(/[^\s\u00a0]/),-1==b&&(b=a.length));for(var f=d||0,g=e||0;b>f;++f)"	"==a.charAt(f)?g+=c-g%c:++g;return g}function Oe(a){for(;a>=Ne.length;)Ne.push(Pe(Ne)+" ");return Ne[a]}function Pe(a){return a[a.length-1]}function Qe(a){n?(a.selectionStart=0,a.selectionEnd=a.value.length):a.select()}function Re(a,b){if(a.indexOf)return a.indexOf(b);for(var c=0,d=a.length;d>c;++c)if(a[c]==b)return c;return-1}function Se(a,b){function c(){}c.prototype=a;var d=new c;return b&&Te(b,d),d}function Te(a,b){b||(b={});for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c]);return b}function Ue(a){for(var b=[],c=0;a>c;++c)b.push(void 0);return b}function Ve(a){var b=Array.prototype.slice.call(arguments,1);return function(){return a.apply(null,b)}}function Xe(a){return/\w/.test(a)||a>"\u0080"&&(a.toUpperCase()!=a.toLowerCase()||We.test(a))}function Ye(a){for(var b in a)if(a.hasOwnProperty(b)&&a[b])return!1;return!0}function $e(a,b,c,d){var e=document.createElement(a);if(c&&(e.className=c),d&&(e.style.cssText=d),"string"==typeof b)bf(e,b);else if(b)for(var f=0;b.length>f;++f)e.appendChild(b[f]);return e}function _e(a){for(var b=a.childNodes.length;b>0;--b)a.removeChild(a.firstChild);return a}function af(a,b){return _e(a).appendChild(b)}function bf(a,b){d?(a.innerHTML="",a.appendChild(document.createTextNode(b))):a.textContent=b}function cf(a){return a.getBoundingClientRect()}function ef(){return!1}function gf(a){if(null!=ff)return ff;var b=$e("div",null,null,"width: 50px; height: 50px; overflow-x: scroll");return af(a,b),b.offsetWidth&&(ff=b.offsetHeight-b.clientHeight),ff||0}function jf(a){if(null==hf){var b=$e("span","\u200b");af(a,$e("span",[b,document.createTextNode("x")])),0!=a.firstChild.offsetHeight&&(hf=1>=b.offsetWidth&&b.offsetHeight>2&&!c)}return hf?$e("span","\u200b"):$e("span","\u00a0",null,"display: inline-block; width: 1px; margin-right: -1px")}function of(a,b,c,d){if(!a)return d(b,c,"ltr");for(var e=0;a.length>e;++e){var f=a[e];(c>f.from&&f.to>b||b==c&&f.to==b)&&d(Math.max(f.from,b),Math.min(f.to,c),1==f.level?"rtl":"ltr")}}function pf(a){return a.level%2?a.to:a.from}function qf(a){return a.level%2?a.from:a.to}function rf(a){var b=je(a);return b?pf(b[0]):0}function sf(a){var b=je(a);return b?qf(Pe(b)):a.text.length}function tf(a,b){var c=ce(a.doc,b),d=wd(a.doc,c);d!=c&&(b=ge(d));var e=je(d),f=e?e[0].level%2?sf(d):rf(d):0;return tc(b,f)}function uf(a,b){for(var c,d;c=vd(d=ce(a.doc,b));)b=c.find().to.line;var e=je(d),f=e?e[0].level%2?rf(d):sf(d):d.text.length;return tc(b,f)}function vf(a,b,c){var d=a[0].level;return b==d?!0:c==d?!1:c>b}function xf(a,b){for(var d,c=0;a.length>c;++c){var e=a[c];if(b>e.from&&e.to>b)return wf=null,c;if(e.from==b||e.to==b){if(null!=d)return vf(a,e.level,a[d].level)?(wf=d,c):(wf=c,d);d=c}}return wf=null,d}function yf(a,b,c,d){if(!d)return b+c;do b+=c;while(b>0&&Ze.test(a.text.charAt(b)));return b}function zf(a,b,c,d){var e=je(a);if(!e)return Af(a,b,c,d);for(var f=xf(e,b),g=e[f],h=yf(a,b,g.level%2?-c:c,d);;){if(h>g.from&&g.to>h)return h;if(h==g.from||h==g.to)return xf(e,h)==f?h:(g=e[f+=c],c>0==g.level%2?g.to:g.from);if(g=e[f+=c],!g)return null;h=c>0==g.level%2?yf(a,g.to,-1,d):yf(a,g.from,1,d)}}function Af(a,b,c,d){var e=b+c;if(d)for(;e>0&&Ze.test(a.text.charAt(e));)e+=c;return 0>e||e>a.text.length?null:e}var a=/gecko\/\d/i.test(navigator.userAgent),b=/MSIE \d/.test(navigator.userAgent),c=b&&(null==document.documentMode||8>document.documentMode),d=b&&(null==document.documentMode||9>document.documentMode),e=/WebKit\//.test(navigator.userAgent),f=e&&/Qt\/\d+\.\d+/.test(navigator.userAgent),g=/Chrome\//.test(navigator.userAgent),h=/Opera\//.test(navigator.userAgent),i=/Apple Computer/.test(navigator.vendor),j=/KHTML\//.test(navigator.userAgent),k=/Mac OS X 1\d\D([7-9]|\d\d)\D/.test(navigator.userAgent),l=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(navigator.userAgent),m=/PhantomJS/.test(navigator.userAgent),n=/AppleWebKit/.test(navigator.userAgent)&&/Mobile\/\w+/.test(navigator.userAgent),o=n||/Android|webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(navigator.userAgent),p=n||/Mac/.test(navigator.platform),q=/windows/i.test(navigator.platform),r=h&&navigator.userAgent.match(/Version\/(\d*\.\d*)/);r&&(r=Number(r[1]));var ub,Nb,Ob,s=p&&(f||h&&(null==r||12.11>r)),t=a||b&&!d,u=!1,v=!1,xb=0,Vb=0,Wb=null;b?Wb=-.53:a?Wb=15:g?Wb=-.7:i&&(Wb=-1/3);var $b,gc,bc=null;w.Pos=tc,w.prototype={constructor:w,focus:function(){window.focus(),Ib(this),ec(this),Fb(this)},setOption:function(a,b){var c=this.options,d=c[a];(c[a]!=b||"mode"==a)&&(c[a]=b,Sc.hasOwnProperty(a)&&Ab(this,Sc[a])(this,b,d))},getOption:function(a){return this.options[a]},getDoc:function(){return this.doc},addKeyMap:function(a,b){this.state.keyMaps[b?"push":"unshift"](a)},removeKeyMap:function(a){for(var b=this.state.keyMaps,c=0;b.length>c;++c)if(("string"==typeof a?b[c].name:b[c])==a)return b.splice(c,1),!0},addOverlay:Ab(null,function(a,b){var c=a.token?a:w.getMode(this.options,a);if(c.startState)throw Error("Overlays may not be stateful.");this.state.overlays.push({mode:c,modeSpec:a,opaque:b&&b.opaque}),this.state.modeGen++,Db(this)}),removeOverlay:Ab(null,function(a){for(var b=this.state.overlays,c=0;b.length>c;++c){var d=b[c].modeSpec;if(d==a||"string"==typeof a&&d.name==a)return b.splice(c,1),this.state.modeGen++,Db(this),void 0}}),indentLine:Ab(null,function(a,b,c){"string"!=typeof b&&(b=null==b?this.options.smartIndent?"smart":"prev":b?"add":"subtract"),Ac(this.doc,a)&&Mc(this,a,b,c)}),indentSelection:Ab(null,function(a){var b=this.doc.sel;if(uc(b.from,b.to))return Mc(this,b.from.line,a);for(var c=b.to.line-(b.to.ch?0:1),d=b.from.line;c>=d;++d)Mc(this,d,a)}),getTokenAt:function(a){var b=this.doc;a=yc(b,a);for(var c=cb(this,a.line),d=this.doc.mode,e=ce(b,a.line),f=new gd(e.text,this.options.tabSize);f.pos<a.ch&&!f.eol();){f.start=f.pos;var g=d.token(f,c)}return{start:f.start,end:f.pos,string:f.current(),className:g||null,type:g||null,state:c}},getStateAfter:function(a){var b=this.doc;return a=xc(b,null==a?b.first+b.size-1:a),cb(this,a+1)},cursorCoords:function(a,b){var c,d=this.doc.sel;return c=null==a?d.head:"object"==typeof a?yc(this.doc,a):a?d.from:d.to,qb(this,c,b||"page")},charCoords:function(a,b){return pb(this,yc(this.doc,a),b||"page")},coordsChar:function(a,b){return a=ob(this,a,b||"page"),sb(this,a.left,a.top)},defaultTextHeight:function(){return vb(this.display)},defaultCharWidth:function(){return wb(this.display)},setGutterMarker:Ab(null,function(a,b,c){return Nc(this,a,function(a){var d=a.gutterMarkers||(a.gutterMarkers={});return d[b]=c,!c&&Ye(d)&&(a.gutterMarkers=null),!0})}),clearGutter:Ab(null,function(a){var b=this,c=b.doc,d=c.first;c.iter(function(c){c.gutterMarkers&&c.gutterMarkers[a]&&(c.gutterMarkers[a]=null,Db(b,d,d+1),Ye(c.gutterMarkers)&&(c.gutterMarkers=null)),++d})}),addLineClass:Ab(null,function(a,b,c){return Nc(this,a,function(a){var d="text"==b?"textClass":"background"==b?"bgClass":"wrapClass";if(a[d]){if(RegExp("\\b"+c+"\\b").test(a[d]))return!1;a[d]+=" "+c}else a[d]=c;return!0})}),removeLineClass:Ab(null,function(a,b,c){return Nc(this,a,function(a){var d="text"==b?"textClass":"background"==b?"bgClass":"wrapClass",e=a[d];if(!e)return!1;if(null==c)a[d]=null;else{var f=e.replace(RegExp("^"+c+"\\b\\s*|\\s*\\b"+c+"\\b"),"");if(f==e)return!1;a[d]=f||null}return!0})}),addLineWidget:Ab(null,function(a,b,c){return Ed(this,a,b,c)}),removeLineWidget:function(a){a.clear()},lineInfo:function(a){if("number"==typeof a){if(!Ac(this.doc,a))return null;var b=a;if(a=ce(this.doc,a),!a)return null}else{var b=ge(a);if(null==b)return null}return{line:b,handle:a,text:a.text,gutterMarkers:a.gutterMarkers,textClass:a.textClass,bgClass:a.bgClass,wrapClass:a.wrapClass,widgets:a.widgets}},getViewport:function(){return{from:this.display.showingFrom,to:this.display.showingTo}},addWidget:function(a,b,c,d,e){var f=this.display;a=qb(this,yc(this.doc,a));var g=a.bottom,h=a.left;if(b.style.position="absolute",f.sizer.appendChild(b),"over"==d)g=a.top;else if("above"==d||"near"==d){var i=Math.max(f.wrapper.clientHeight,this.doc.height),j=Math.max(f.sizer.clientWidth,f.lineSpace.clientWidth);("above"==d||a.bottom+b.offsetHeight>i)&&a.top>b.offsetHeight?g=a.top-b.offsetHeight:i>=a.bottom+b.offsetHeight&&(g=a.bottom),h+b.offsetWidth>j&&(h=j-b.offsetWidth)}b.style.top=g+db(f)+"px",b.style.left=b.style.right="","right"==e?(h=f.sizer.clientWidth-b.offsetWidth,b.style.right="0px"):("left"==e?h=0:"middle"==e&&(h=(f.sizer.clientWidth-b.offsetWidth)/2),b.style.left=h+"px"),c&&Ic(this,h,g,h+b.offsetWidth,g+b.offsetHeight)},triggerOnKeyDown:Ab(null,cc),execCommand:function(a){return ad[a](this)},findPosH:function(a,b,c,d){var e=1;0>b&&(e=-1,b=-b);for(var f=0,g=yc(this.doc,a);b>f&&(g=Oc(this.doc,g,e,c,d),!g.hitSide);++f);return g},moveH:Ab(null,function(a,b){var d,c=this.doc.sel;d=c.shift||c.extend||uc(c.from,c.to)?Oc(this.doc,c.head,a,b,this.options.rtlMoveVisually):0>a?c.from:c.to,Bc(this.doc,d,d,a)}),deleteH:Ab(null,function(a,b){var c=this.doc.sel;uc(c.from,c.to)?sc(this.doc,"",c.from,Oc(this.doc,c.head,a,b,!1),"+delete"):sc(this.doc,"",c.from,c.to,"+delete"),this.curOp.userSelChange=!0}),findPosV:function(a,b,c,d){var e=1,f=d;0>b&&(e=-1,b=-b);for(var g=0,h=yc(this.doc,a);b>g;++g){var i=qb(this,h,"div");if(null==f?f=i.left:i.left=f,h=Pc(this,i,e,c),h.hitSide)break}return h},moveV:Ab(null,function(a,b){var c=this.doc.sel,d=qb(this,c.head,"div");null!=c.goalColumn&&(d.left=c.goalColumn);var e=Pc(this,d,a,b);"page"==b&&Lc(this,0,pb(this,e,"div").top-d.top),Bc(this.doc,e,e,a),c.goalColumn=d.left}),toggleOverwrite:function(a){(null==a||a!=this.state.overwrite)&&((this.state.overwrite=!this.state.overwrite)?this.display.cursor.className+=" CodeMirror-overwrite":this.display.cursor.className=this.display.cursor.className.replace(" CodeMirror-overwrite",""))},hasFocus:function(){return this.state.focused},scrollTo:Ab(null,function(a,b){Kc(this,a,b)}),getScrollInfo:function(){var a=this.display.scroller,b=Je;return{left:a.scrollLeft,top:a.scrollTop,height:a.scrollHeight-b,width:a.scrollWidth-b,clientHeight:a.clientHeight-b,clientWidth:a.clientWidth-b}},scrollIntoView:Ab(null,function(a,b){"number"==typeof a&&(a=tc(a,0)),b||(b=0);var c=a;a&&null==a.line||(this.curOp.scrollToPos=a?yc(this.doc,a):this.doc.sel.head,this.curOp.scrollToPosMargin=b,c=qb(this,this.curOp.scrollToPos));var d=Jc(this,c.left,c.top-b,c.right,c.bottom+b);Kc(this,d.scrollLeft,d.scrollTop)}),setSize:function(a,b){function c(a){return"number"==typeof a||/^\d+$/.test(a+"")?a+"px":a}null!=a&&(this.display.wrapper.style.width=c(a)),null!=b&&(this.display.wrapper.style.height=c(b)),this.refresh()},on:function(a,b){Be(this,a,b)},off:function(a,b){Ce(this,a,b)},operation:function(a){return Cb(this,a)},refresh:Ab(null,function(){mb(this),Kc(this,this.doc.scrollLeft,this.doc.scrollTop),Db(this)}),swapDoc:Ab(null,function(a){var b=this.doc;return b.cm=null,be(this,a),mb(this),Hb(this,!0),Kc(this,a.scrollLeft,a.scrollTop),b}),getInputField:function(){return this.display.input},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}};var Sc=w.optionHandlers={},Tc=w.defaults={},Vc=w.Init={toString:function(){return"CodeMirror.Init"}};Uc("value","",function(a,b){a.setValue(b)},!0),Uc("mode",null,function(a,b){a.doc.modeOption=b,y(a)},!0),Uc("indentUnit",2,y,!0),Uc("indentWithTabs",!1),Uc("smartIndent",!0),Uc("tabSize",4,function(a){y(a),mb(a),Db(a)},!0),Uc("electricChars",!0),Uc("rtlMoveVisually",!q),Uc("theme","default",function(a){D(a),E(a)},!0),Uc("keyMap","default",C),Uc("extraKeys",null),Uc("onKeyEvent",null),Uc("onDragEvent",null),Uc("lineWrapping",!1,z,!0),Uc("gutters",[],function(a){I(a.options),E(a)},!0),Uc("fixedGutter",!0,function(a,b){a.display.gutters.style.left=b?O(a.display)+"px":"0",a.refresh()},!0),Uc("coverGutterNextToScrollbar",!1,J,!0),Uc("lineNumbers",!1,function(a){I(a.options),E(a)},!0),Uc("firstLineNumber",1,E,!0),Uc("lineNumberFormatter",function(a){return a},E,!0),Uc("showCursorWhenSelecting",!1,X,!0),Uc("readOnly",!1,function(a,b){"nocursor"==b?(fc(a),a.display.input.blur()):b||Hb(a,!0)}),Uc("dragDrop",!0),Uc("cursorBlinkRate",530),Uc("cursorScrollMargin",0),Uc("cursorHeight",1),Uc("workTime",100),Uc("workDelay",100),Uc("flattenSpans",!0),Uc("pollInterval",100),Uc("undoDepth",40,function(a,b){a.doc.history.undoDepth=b}),Uc("historyEventDelay",500),Uc("viewportMargin",10,function(a){a.refresh()},!0),Uc("maxHighlightLength",1e4,function(a){y(a),a.refresh()},!0),Uc("moveInputWithCursor",!0,function(a,b){b||(a.display.inputDiv.style.top=a.display.inputDiv.style.left=0)}),Uc("tabindex",null,function(a,b){a.display.input.tabIndex=b||""}),Uc("autofocus",null);var Wc=w.modes={},Xc=w.mimeModes={};w.defineMode=function(a,b){if(w.defaults.mode||"null"==a||(w.defaults.mode=a),arguments.length>2){b.dependencies=[];for(var c=2;arguments.length>c;++c)b.dependencies.push(arguments[c])}Wc[a]=b},w.defineMIME=function(a,b){Xc[a]=b},w.resolveMode=function(a){if("string"==typeof a&&Xc.hasOwnProperty(a))a=Xc[a];else if(a&&"string"==typeof a.name&&Xc.hasOwnProperty(a.name)){var b=Xc[a.name];a=Se(b,a),a.name=b.name}else if("string"==typeof a&&/^[\w\-]+\/[\w\-]+\+xml$/.test(a))return w.resolveMode("application/xml");return"string"==typeof a?{name:a}:a||{name:"null"}},w.getMode=function(a,b){b=w.resolveMode(b);var c=Wc[b.name];if(!c)return w.getMode(a,"text/plain");var d=c(a,b);if(Yc.hasOwnProperty(b.name)){var e=Yc[b.name];for(var f in e)e.hasOwnProperty(f)&&(d.hasOwnProperty(f)&&(d["_"+f]=d[f]),d[f]=e[f])}return d.name=b.name,d},w.defineMode("null",function(){return{token:function(a){a.skipToEnd()}}}),w.defineMIME("text/plain","null");var Yc=w.modeExtensions={};w.extendMode=function(a,b){var c=Yc.hasOwnProperty(a)?Yc[a]:Yc[a]={};Te(b,c)},w.defineExtension=function(a,b){w.prototype[a]=b},w.defineDocExtension=function(a,b){Zd.prototype[a]=b},w.defineOption=Uc;var Zc=[];w.defineInitHook=function(a){Zc.push(a)},w.copyState=$c,w.startState=_c,w.innerMode=function(a,b){for(;a.innerMode;){var c=a.innerMode(b);b=c.state,a=c.mode}return c||{mode:a,state:b}};var ad=w.commands={selectAll:function(a){a.setSelection(tc(a.firstLine(),0),tc(a.lastLine()))},killLine:function(a){var b=a.getCursor(!0),c=a.getCursor(!1),d=!uc(b,c);d||a.getLine(b.line).length!=b.ch?a.replaceRange("",b,d?c:tc(b.line),"+delete"):a.replaceRange("",b,tc(b.line+1,0),"+delete")},deleteLine:function(a){var b=a.getCursor().line;a.replaceRange("",tc(b,0),tc(b),"+delete")},undo:function(a){a.undo()},redo:function(a){a.redo()},goDocStart:function(a){a.extendSelection(tc(a.firstLine(),0))},goDocEnd:function(a){a.extendSelection(tc(a.lastLine()))},goLineStart:function(a){a.extendSelection(tf(a,a.getCursor().line))},goLineStartSmart:function(a){var b=a.getCursor(),c=tf(a,b.line),d=a.getLineHandle(c.line),e=je(d);if(e&&0!=e[0].level)a.extendSelection(c);else{var f=Math.max(0,d.text.search(/\S/)),g=b.line==c.line&&f>=b.ch&&b.ch;a.extendSelection(tc(c.line,g?0:f))}},goLineEnd:function(a){a.extendSelection(uf(a,a.getCursor().line))},goLineRight:function(a){var b=a.charCoords(a.getCursor(),"div").top+5;a.extendSelection(a.coordsChar({left:a.display.lineDiv.offsetWidth+100,top:b},"div"))},goLineLeft:function(a){var b=a.charCoords(a.getCursor(),"div").top+5;a.extendSelection(a.coordsChar({left:0,top:b},"div"))},goLineUp:function(a){a.moveV(-1,"line")},goLineDown:function(a){a.moveV(1,"line")},goPageUp:function(a){a.moveV(-1,"page")},goPageDown:function(a){a.moveV(1,"page")},goCharLeft:function(a){a.moveH(-1,"char")},goCharRight:function(a){a.moveH(1,"char")},goColumnLeft:function(a){a.moveH(-1,"column")},goColumnRight:function(a){a.moveH(1,"column")},goWordLeft:function(a){a.moveH(-1,"word")},goGroupRight:function(a){a.moveH(1,"group")},goGroupLeft:function(a){a.moveH(-1,"group")},goWordRight:function(a){a.moveH(1,"word")},delCharBefore:function(a){a.deleteH(-1,"char")},delCharAfter:function(a){a.deleteH(1,"char")},delWordBefore:function(a){a.deleteH(-1,"word")},delWordAfter:function(a){a.deleteH(1,"word")},delGroupBefore:function(a){a.deleteH(-1,"group")},delGroupAfter:function(a){a.deleteH(1,"group")},indentAuto:function(a){a.indentSelection("smart")},indentMore:function(a){a.indentSelection("add")},indentLess:function(a){a.indentSelection("subtract")},insertTab:function(a){a.replaceSelection("	","end","+input")},defaultTab:function(a){a.somethingSelected()?a.indentSelection("add"):a.replaceSelection("	","end","+input")},transposeChars:function(a){var b=a.getCursor(),c=a.getLine(b.line);b.ch>0&&b.ch<c.length-1&&a.replaceRange(c.charAt(b.ch)+c.charAt(b.ch-1),tc(b.line,b.ch-1),tc(b.line,b.ch+1))},newlineAndIndent:function(a){Ab(a,function(){a.replaceSelection("\n","end","+input"),a.indentLine(a.getCursor().line,null,!0)})()},toggleOverwrite:function(a){a.toggleOverwrite()}},bd=w.keyMap={};bd.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite"},bd.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Alt-Up":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Down":"goDocEnd","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore",fallthrough:"basic"},bd.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineStart","Cmd-Right":"goLineEnd","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore",fallthrough:["basic","emacsy"]},bd["default"]=p?bd.macDefault:bd.pcDefault,bd.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-D":"delWordAfter","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars"},w.lookupKey=dd,w.isModifierKey=ed,w.keyName=fd,w.fromTextArea=function(a,b){function e(){a.value=i.getValue()}if(b||(b={}),b.value=a.value,!b.tabindex&&a.tabindex&&(b.tabindex=a.tabindex),!b.placeholder&&a.placeholder&&(b.placeholder=a.placeholder),null==b.autofocus){var c=document.body;try{c=document.activeElement}catch(d){}b.autofocus=c==a||null!=a.getAttribute("autofocus")&&c==document.body}if(a.form&&(Be(a.form,"submit",e),!b.leaveSubmitMethodAlone)){var f=a.form,g=f.submit;try{var h=f.submit=function(){e(),f.submit=g,f.submit(),f.submit=h}}catch(d){}}a.style.display="none";var i=w(function(b){a.parentNode.insertBefore(b,a.nextSibling)},b);return i.save=e,i.getTextArea=function(){return a},i.toTextArea=function(){e(),a.parentNode.removeChild(i.getWrapperElement()),a.style.display="",a.form&&(Ce(a.form,"submit",e),"function"==typeof a.form.submit&&(a.form.submit=g))},i},gd.prototype={eol:function(){return this.pos>=this.string.length},sol:function(){return 0==this.pos},peek:function(){return this.string.charAt(this.pos)||void 0},next:function(){return this.pos<this.string.length?this.string.charAt(this.pos++):void 0},eat:function(a){var b=this.string.charAt(this.pos);if("string"==typeof a)var c=b==a;else var c=b&&(a.test?a.test(b):a(b));return c?(++this.pos,b):void 0},eatWhile:function(a){for(var b=this.pos;this.eat(a););return this.pos>b},eatSpace:function(){for(var a=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>a},skipToEnd:function(){this.pos=this.string.length},skipTo:function(a){var b=this.string.indexOf(a,this.pos);return b>-1?(this.pos=b,!0):void 0},backUp:function(a){this.pos-=a},column:function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=Me(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue},indentation:function(){return Me(this.string,null,this.tabSize)},match:function(a,b,c){if("string"!=typeof a){var f=this.string.slice(this.pos).match(a);return f&&f.index>0?null:(f&&b!==!1&&(this.pos+=f[0].length),f)}var d=function(a){return c?a.toLowerCase():a},e=this.string.substr(this.pos,a.length);return d(e)==d(a)?(b!==!1&&(this.pos+=a.length),!0):void 0},current:function(){return this.string.slice(this.start,this.pos)}},w.StringStream=gd,w.TextMarker=hd,hd.prototype.clear=function(){if(!this.explicitlyCleared){var a=this.doc.cm,b=a&&!a.curOp;b&&yb(a);for(var c=null,d=null,e=0;this.lines.length>e;++e){var f=this.lines[e],g=ld(f.markedSpans,this);null!=g.to&&(d=ge(f)),f.markedSpans=md(f.markedSpans,g),null!=g.from?c=ge(f):this.collapsed&&!xd(this.doc,f)&&a&&fe(f,vb(a.display))}if(a&&this.collapsed&&!a.options.lineWrapping)for(var e=0;this.lines.length>e;++e){var h=wd(a.doc,this.lines[e]),i=G(a.doc,h);i>a.display.maxLineLength&&(a.display.maxLine=h,a.display.maxLineLength=i,a.display.maxLineChanged=!0)}null!=c&&a&&Db(a,c,d+1),this.lines.length=0,this.explicitlyCleared=!0,this.collapsed&&this.doc.cantEdit&&(this.doc.cantEdit=!1,a&&Ec(a)),b&&zb(a),Ge(this,"clear")}},hd.prototype.find=function(){for(var a,b,c=0;this.lines.length>c;++c){var d=this.lines[c],e=ld(d.markedSpans,this);if(null!=e.from||null!=e.to){var f=ge(d);null!=e.from&&(a=tc(f,e.from)),null!=e.to&&(b=tc(f,e.to))}}return"bookmark"==this.type?a:a&&{from:a,to:b}},hd.prototype.changed=function(){var a=this.find(),b=this.doc.cm;if(a&&b){var c=ce(this.doc,a.from.line);if(ib(b,c),a.from.line>=b.display.showingFrom&&a.from.line<b.display.showingTo){for(var d=b.display.lineDiv.firstChild;d;d=d.nextSibling)if(d.lineObj==c){d.offsetHeight!=c.height&&fe(c,d.offsetHeight);break}Cb(b,function(){b.curOp.selectionChanged=!0})}}},hd.prototype.attachLine=function(a){if(!this.lines.length&&this.doc.cm){var b=this.doc.cm.curOp;b.maybeHiddenMarkers&&-1!=Re(b.maybeHiddenMarkers,this)||(b.maybeUnhiddenMarkers||(b.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(a)},hd.prototype.detachLine=function(a){if(this.lines.splice(Re(this.lines,a),1),!this.lines.length&&this.doc.cm){var b=this.doc.cm.curOp;(b.maybeHiddenMarkers||(b.maybeHiddenMarkers=[])).push(this)}},w.SharedTextMarker=jd,jd.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var a=0;this.markers.length>a;++a)this.markers[a].clear();Ge(this,"clear")}},jd.prototype.find=function(){return this.primary.find()};var Bd=w.LineWidget=function(a,b,c){for(var d in c)c.hasOwnProperty(d)&&(this[d]=c[d]);this.cm=a,this.node=b};Bd.prototype.clear=Cd(function(){var a=this.line.widgets,b=ge(this.line);if(null!=b&&a){for(var c=0;a.length>c;++c)a[c]==this&&a.splice(c--,1);a.length||(this.line.widgets=null),fe(this.line,Math.max(0,this.line.height-Dd(this))),Db(this.cm,b,b+1)}}),Bd.prototype.changed=Cd(function(){var a=this.height;this.height=null;var b=Dd(this)-a;if(b){fe(this.line,this.line.height+b);var c=ge(this.line);Db(this.cm,c,c+1)}});var Md={},Pd=/[\t\u0000-\u0019\u00ad\u200b\u2028\u2029\uFEFF]/g;Wd.prototype={chunkSize:function(){return this.lines.length},removeInner:function(a,b){for(var c=a,d=a+b;d>c;++c){var e=this.lines[c];this.height-=e.height,Hd(e),Ge(e,"delete")}this.lines.splice(a,b)},collapse:function(a){a.splice.apply(a,[a.length,0].concat(this.lines))},insertInner:function(a,b,c){this.height+=c,this.lines=this.lines.slice(0,a).concat(b).concat(this.lines.slice(a));for(var d=0,e=b.length;e>d;++d)b[d].parent=this},iterN:function(a,b,c){for(var d=a+b;d>a;++a)if(c(this.lines[a]))return!0}},Xd.prototype={chunkSize:function(){return this.size},removeInner:function(a,b){this.size-=b;for(var c=0;this.children.length>c;++c){var d=this.children[c],e=d.chunkSize();if(e>a){var f=Math.min(b,e-a),g=d.height;if(d.removeInner(a,f),this.height-=g-d.height,e==f&&(this.children.splice(c--,1),d.parent=null),0==(b-=f))break;a=0}else a-=e}if(25>this.size-b){var h=[];this.collapse(h),this.children=[new Wd(h)],this.children[0].parent=this}},collapse:function(a){for(var b=0,c=this.children.length;c>b;++b)this.children[b].collapse(a)},insertInner:function(a,b,c){this.size+=b.length,this.height+=c;for(var d=0,e=this.children.length;e>d;++d){var f=this.children[d],g=f.chunkSize();if(g>=a){if(f.insertInner(a,b,c),f.lines&&f.lines.length>50){for(;f.lines.length>50;){var h=f.lines.splice(f.lines.length-25,25),i=new Wd(h);f.height-=i.height,this.children.splice(d+1,0,i),i.parent=this}this.maybeSpill()}break}a-=g}},maybeSpill:function(){if(!(10>=this.children.length)){var a=this;do{var b=a.children.splice(a.children.length-5,5),c=new Xd(b);if(a.parent){a.size-=c.size,a.height-=c.height;var e=Re(a.parent.children,a);a.parent.children.splice(e+1,0,c)}else{var d=new Xd(a.children);d.parent=a,a.children=[d,c],a=d}c.parent=a.parent}while(a.children.length>10);a.parent.maybeSpill()}},iterN:function(a,b,c){for(var d=0,e=this.children.length;e>d;++d){var f=this.children[d],g=f.chunkSize();if(g>a){var h=Math.min(b,g-a);if(f.iterN(a,h,c))return!0;if(0==(b-=h))break;a=0}else a-=g}}};var Yd=0,Zd=w.Doc=function(a,b,c){if(!(this instanceof Zd))return new Zd(a,b,c);null==c&&(c=0),Xd.call(this,[new Wd([Fd("",null)])]),this.first=c,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.history=ke(),this.frontier=c;var d=tc(c,0);this.sel={from:d,to:d,head:d,anchor:d,shift:!1,extend:!1,goalColumn:null},this.id=++Yd,this.modeOption=b,"string"==typeof a&&(a=kf(a)),Vd(this,{from:d,to:d,text:a},null,{head:d,anchor:d})};Zd.prototype=Se(Xd.prototype,{constructor:Zd,iter:function(a,b,c){c?this.iterN(a-this.first,b-a,c):this.iterN(this.first,this.first+this.size,a)},insert:function(a,b){for(var c=0,d=0,e=b.length;e>d;++d)c+=b[d].height;this.insertInner(a-this.first,b,c)},remove:function(a,b){this.removeInner(a-this.first,b)},getValue:function(a){var b=ee(this,this.first,this.first+this.size);return a===!1?b:b.join(a||"\n")},setValue:function(a){var b=tc(this.first,0),c=this.first+this.size-1;mc(this,{from:b,to:tc(c,ce(this,c).text.length),text:kf(a),origin:"setValue"},{head:b,anchor:b},!0)},replaceRange:function(a,b,c,d){b=yc(this,b),c=c?yc(this,c):b,sc(this,a,b,c,d)},getRange:function(a,b,c){var d=de(this,yc(this,a),yc(this,b));return c===!1?d:d.join(c||"\n")},getLine:function(a){var b=this.getLineHandle(a);return b&&b.text},setLine:function(a,b){Ac(this,a)&&sc(this,b,tc(a,0),yc(this,tc(a)))},removeLine:function(a){a?sc(this,"",yc(this,tc(a-1)),yc(this,tc(a))):sc(this,"",tc(0,0),yc(this,tc(1,0)))},getLineHandle:function(a){return Ac(this,a)?ce(this,a):void 0},getLineNumber:function(a){return ge(a)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(a){return yc(this,a)},getCursor:function(a){var c,b=this.sel;return c=null==a||"head"==a?b.head:"anchor"==a?b.anchor:"end"==a||a===!1?b.to:b.from,wc(c)},somethingSelected:function(){return!uc(this.sel.head,this.sel.anchor)},setCursor:Bb(function(a,b,c){var d=yc(this,"number"==typeof a?tc(a,b||0):a);c?Bc(this,d):Dc(this,d,d)}),setSelection:Bb(function(a,b){Dc(this,yc(this,a),yc(this,b||a))}),extendSelection:Bb(function(a,b){Bc(this,yc(this,a),b&&yc(this,b))}),getSelection:function(a){return this.getRange(this.sel.from,this.sel.to,a)},replaceSelection:function(a,b,c){mc(this,{from:this.sel.from,to:this.sel.to,text:kf(a),origin:c},b||"around")},undo:Bb(function(){oc(this,"undo")}),redo:Bb(function(){oc(this,"redo")}),setExtending:function(a){this.sel.extend=a},historySize:function(){var a=this.history;return{undo:a.done.length,redo:a.undone.length}},clearHistory:function(){this.history=ke()},markClean:function(){this.history.dirtyCounter=0,this.history.lastOp=this.history.lastOrigin=null},isClean:function(){return 0==this.history.dirtyCounter},getHistory:function(){return{done:qe(this.history.done),undone:qe(this.history.undone)}},setHistory:function(a){var b=this.history=ke();b.done=a.done.slice(0),b.undone=a.undone.slice(0)},markText:function(a,b,c){return id(this,yc(this,a),yc(this,b),c,"range")},setBookmark:function(a,b){var c={replacedWith:b&&(null==b.nodeType?b.widget:b),insertLeft:b&&b.insertLeft};return a=yc(this,a),id(this,a,a,c,"bookmark")},findMarksAt:function(a){a=yc(this,a);var b=[],c=ce(this,a.line).markedSpans;
if(c)for(var d=0;c.length>d;++d){var e=c[d];(null==e.from||e.from<=a.ch)&&(null==e.to||e.to>=a.ch)&&b.push(e.marker.parent||e.marker)}return b},getAllMarks:function(){var a=[];return this.iter(function(b){var c=b.markedSpans;if(c)for(var d=0;c.length>d;++d)null!=c[d].from&&a.push(c[d].marker)}),a},posFromIndex:function(a){var b,c=this.first;return this.iter(function(d){var e=d.text.length+1;return e>a?(b=a,!0):(a-=e,++c,void 0)}),yc(this,tc(c,b))},indexFromPos:function(a){a=yc(this,a);var b=a.ch;return a.line<this.first||0>a.ch?0:(this.iter(this.first,a.line,function(a){b+=a.text.length+1}),b)},copy:function(a){var b=new Zd(ee(this,this.first,this.first+this.size),this.modeOption,this.first);return b.scrollTop=this.scrollTop,b.scrollLeft=this.scrollLeft,b.sel={from:this.sel.from,to:this.sel.to,head:this.sel.head,anchor:this.sel.anchor,shift:this.sel.shift,extend:!1,goalColumn:this.sel.goalColumn},a&&(b.history.undoDepth=this.history.undoDepth,b.setHistory(this.getHistory())),b},linkedDoc:function(a){a||(a={});var b=this.first,c=this.first+this.size;null!=a.from&&a.from>b&&(b=a.from),null!=a.to&&c>a.to&&(c=a.to);var d=new Zd(ee(this,b,c),a.mode||this.modeOption,b);return a.sharedHist&&(d.history=this.history),(this.linked||(this.linked=[])).push({doc:d,sharedHist:a.sharedHist}),d.linked=[{doc:this,isParent:!0,sharedHist:a.sharedHist}],d},unlinkDoc:function(a){if(a instanceof w&&(a=a.doc),this.linked)for(var b=0;this.linked.length>b;++b){var c=this.linked[b];if(c.doc==a){this.linked.splice(b,1),a.unlinkDoc(this);break}}if(a.history==this.history){var d=[a.id];ae(a,function(a){d.push(a.id)},!0),a.history=ke(),a.history.done=qe(this.history.done,d),a.history.undone=qe(this.history.undone,d)}},iterLinkedDocs:function(a){ae(this,a)},getMode:function(){return this.mode},getEditor:function(){return this.cm}}),Zd.prototype.eachLine=Zd.prototype.iter;var $d="iter insert remove copy getEditor".split(" ");for(var _d in Zd.prototype)Zd.prototype.hasOwnProperty(_d)&&0>Re($d,_d)&&(w.prototype[_d]=function(a){return function(){return a.apply(this.doc,arguments)}}(Zd.prototype[_d]));w.e_stop=ye,w.e_preventDefault=we,w.e_stopPropagation=xe;var Ee,Fe=0;w.on=Be,w.off=Ce,w.signal=De;var Je=30,Ke=w.Pass={toString:function(){return"CodeMirror.Pass"}};Le.prototype={set:function(a,b){clearTimeout(this.id),this.id=setTimeout(b,a)}},w.countColumn=Me;var Ne=[""],We=/[\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/,Ze=/[\u0300-\u036F\u0483-\u0487\u0488-\u0489\u0591-\u05BD\u05BF\u05C1-\u05C2\u05C4-\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7-\u06E8\u06EA-\u06ED\uA66F\uA670-\uA672\uA674-\uA67D\uA69F\udc00-\udfff]/;w.replaceGetRect=function(a){cf=a};var df=function(){if(d)return!1;var a=$e("div");return"draggable"in a||"dragDrop"in a}();a?ef=function(a,b){return 36==a.charCodeAt(b-1)&&39==a.charCodeAt(b)}:i&&!/Version\/([6-9]|\d\d)\b/.test(navigator.userAgent)?ef=function(a,b){return/\-[^ \-?]|\?[^ !\'\"\),.\-\/:;\?\]\}]/.test(a.slice(b-1,b+1))}:e&&(ef=function(a,b){return b>1&&45==a.charCodeAt(b-1)&&/\w/.test(a.charAt(b-2))&&/[^\-?\.]/.test(a.charAt(b))?!0:/[~!#%&*)=+}\]|\"\.>,:;][({[<]|\?[\w~`@#$%\^&*(_=+{[|><]/.test(a.slice(b-1,b+1))});var ff,hf,kf=3!="\n\nb".split(/\n/).length?function(a){for(var b=0,c=[],d=a.length;d>=b;){var e=a.indexOf("\n",b);-1==e&&(e=a.length);var f=a.slice(b,"\r"==a.charAt(e-1)?e-1:e),g=f.indexOf("\r");-1!=g?(c.push(f.slice(0,g)),b+=g+1):(c.push(f),b=e+1)}return c}:function(a){return a.split(/\r\n?|\n/)};w.splitLines=kf;var lf=window.getSelection?function(a){try{return a.selectionStart!=a.selectionEnd}catch(b){return!1}}:function(a){try{var b=a.ownerDocument.selection.createRange()}catch(c){}return b&&b.parentElement()==a?0!=b.compareEndPoints("StartToEnd",b):!1},mf=function(){var a=$e("div");return"oncopy"in a?!0:(a.setAttribute("oncopy","return;"),"function"==typeof a.oncopy)}(),nf={3:"Enter",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",91:"Mod",92:"Mod",93:"Mod",109:"-",107:"=",127:"Delete",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",63276:"PageUp",63277:"PageDown",63275:"End",63273:"Home",63234:"Left",63232:"Up",63235:"Right",63233:"Down",63302:"Insert",63272:"Delete"};w.keyNames=nf,function(){for(var a=0;10>a;a++)nf[a+48]=a+"";for(var a=65;90>=a;a++)nf[a]=String.fromCharCode(a);for(var a=1;12>=a;a++)nf[a+111]=nf[a+63235]="F"+a}();var wf,Bf=function(){function c(c){return 255>=c?a.charAt(c):c>=1424&&1524>=c?"R":c>=1536&&1791>=c?b.charAt(c-1536):c>=1792&&2220>=c?"r":"L"}var a="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLL",b="rrrrrrrrrrrr,rNNmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmrrrrrrrnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmNmmmmrrrrrrrrrrrrrrrrrr",d=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,e=/[stwN]/,f=/[LRr]/,g=/[Lb1n]/,h=/[1n]/,i="L";return function(a){if(!d.test(a))return!1;for(var l,b=a.length,j=[],k=0;b>k;++k)j.push(l=c(a.charCodeAt(k)));for(var k=0,m=i;b>k;++k){var l=j[k];"m"==l?j[k]=m:m=l}for(var k=0,n=i;b>k;++k){var l=j[k];"1"==l&&"r"==n?j[k]="n":f.test(l)&&(n=l,"r"==l&&(j[k]="R"))}for(var k=1,m=j[0];b-1>k;++k){var l=j[k];"+"==l&&"1"==m&&"1"==j[k+1]?j[k]="1":","!=l||m!=j[k+1]||"1"!=m&&"n"!=m||(j[k]=m),m=l}for(var k=0;b>k;++k){var l=j[k];if(","==l)j[k]="N";else if("%"==l){for(var o=k+1;b>o&&"%"==j[o];++o);for(var p=k&&"!"==j[k-1]||b-1>o&&"1"==j[o]?"1":"N",q=k;o>q;++q)j[q]=p;k=o-1}}for(var k=0,n=i;b>k;++k){var l=j[k];"L"==n&&"1"==l?j[k]="L":f.test(l)&&(n=l)}for(var k=0;b>k;++k)if(e.test(j[k])){for(var o=k+1;b>o&&e.test(j[o]);++o);for(var r="L"==(k?j[k-1]:i),s="L"==(b-1>o?j[o]:i),p=r||s?"L":"R",q=k;o>q;++q)j[q]=p;k=o-1}for(var u,t=[],k=0;b>k;)if(g.test(j[k])){var v=k;for(++k;b>k&&g.test(j[k]);++k);t.push({from:v,to:k,level:0})}else{var w=k,x=t.length;for(++k;b>k&&"L"!=j[k];++k);for(var q=w;k>q;)if(h.test(j[q])){q>w&&t.splice(x,0,{from:w,to:q,level:1});var y=q;for(++q;k>q&&h.test(j[q]);++q);t.splice(x,0,{from:y,to:q,level:2}),w=q}else++q;k>w&&t.splice(x,0,{from:w,to:k,level:1})}return 1==t[0].level&&(u=a.match(/^\s+/))&&(t[0].from=u[0].length,t.unshift({from:0,to:u[0].length,level:0})),1==Pe(t).level&&(u=a.match(/\s+$/))&&(Pe(t).to-=u[0].length,t.push({from:b-u[0].length,to:b,level:0})),t[0].level!=Pe(t).level&&t.push({from:b,to:b,level:t[0].level}),t}}();return w.version="3.13 +",w}(),CodeMirror.defineMode("css",function(a){return CodeMirror.getMode(a,"text/css")}),CodeMirror.defineMode("css-base",function(a,b){"use strict";function l(a,b){return k=b,a}function m(a,b){var c=a.next();if(d[c]){var e=d[c](a,b);if(e!==!1)return e}if("@"==c)return a.eatWhile(/[\w\\\-]/),l("def",a.current());if("="==c)l(null,"compare");else{if(("~"==c||"|"==c)&&a.eat("="))return l(null,"compare");if('"'==c||"'"==c)return b.tokenize=n(c),b.tokenize(a,b);if("#"==c)return a.eatWhile(/[\w\\\-]/),l("atom","hash");if("!"==c)return a.match(/^\s*\w*/),l("keyword","important");if(/\d/.test(c))return a.eatWhile(/[\w.%]/),l("number","unit");if("-"!==c)return/[,+>*\/]/.test(c)?l(null,"select-op"):"."==c&&a.match(/^-?[_a-z][_a-z0-9-]*/i)?l("qualifier","qualifier"):":"==c?l("operator",c):/[;{}\[\]\(\)]/.test(c)?l(null,c):"u"==c&&a.match("rl(")?(a.backUp(1),b.tokenize=o,l("property","variable")):(a.eatWhile(/[\w\\\-]/),l("property","variable"));if(/\d/.test(a.peek()))return a.eatWhile(/[\w.%]/),l("number","unit");if(a.match(/^[^-]+-/))return l("meta","meta")}}function n(a,b){return function(c,d){for(var f,e=!1;null!=(f=c.next())&&(f!=a||e);)e=!e&&"\\"==f;return e||(b&&c.backUp(1),d.tokenize=m),l("string","string")}}function o(a,b){return a.next(),b.tokenize=a.match(/\s*[\"\']/,!1)?m:n(")",!0),l(null,"(")}var c=a.indentUnit,d=b.hooks||{},e=b.atMediaTypes||{},f=b.atMediaFeatures||{},g=b.propertyKeywords||{},h=b.colorKeywords||{},i=b.valueKeywords||{},j=!!b.allowNested,k=null;return{startState:function(a){return{tokenize:m,baseIndent:a||0,stack:[]}},token:function(a,b){if(b.tokenize=b.tokenize||m,b.tokenize==m&&a.eatSpace())return null;var c=b.tokenize(a,b);c&&"string"!=typeof c&&(c=l(c[0],c[1]));var d=b.stack[b.stack.length-1];if("variable"==c)return"variable-definition"==k&&b.stack.push("propertyValue"),"variable-2";if("property"==c){var n=a.current().toLowerCase();"propertyValue"==d?c=i.hasOwnProperty(n)?"string-2":h.hasOwnProperty(n)?"keyword":"variable-2":"rule"==d?g.hasOwnProperty(n)||(c+=" error"):"block"==d?c=g.hasOwnProperty(n)?"property":h.hasOwnProperty(n)?"keyword":i.hasOwnProperty(n)?"string-2":"tag":d&&"@media{"!=d?"@media"==d?c=e[a.current()]?"attribute":/^(only|not)$/.test(n)?"keyword":"and"==n?"error":f.hasOwnProperty(n)?"error":"attribute error":"@mediaType"==d?c=e.hasOwnProperty(n)?"attribute":"and"==n?"operator":/^(only|not)$/.test(n)?"error":"error":"@mediaType("==d?g.hasOwnProperty(n)||(e.hasOwnProperty(n)?c="error":"and"==n?c="operator":/^(only|not)$/.test(n)?c="error":c+=" error"):c="@import"==d?"tag":"error":c="tag"}else"atom"==c?d&&"@media{"!=d&&"block"!=d?"propertyValue"==d?/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(a.current())||(c+=" error"):c="error":c="builtin":"@media"==d&&"{"==k&&(c="error");if("{"==k)if("@media"==d||"@mediaType"==d)b.stack.pop(),b.stack[b.stack.length-1]="@media{";else{var o=j?"block":"rule";b.stack.push(o)}else if("}"==k){var p=b.stack[b.stack.length-1];"interpolation"==p&&(c="operator"),b.stack.pop(),"propertyValue"==d&&b.stack.pop()}else"interpolation"==k?b.stack.push("interpolation"):"@media"==k?b.stack.push("@media"):"@import"==k?b.stack.push("@import"):"@media"==d&&/\b(keyword|attribute)\b/.test(c)?b.stack.push("@mediaType"):"@mediaType"==d&&","==a.current()?b.stack.pop():"@mediaType"==d&&"("==k?b.stack.push("@mediaType("):"@mediaType("==d&&")"==k?b.stack.pop():"rule"!=d&&"block"!=d||":"!=k?"propertyValue"==d&&";"==k?b.stack.pop():"@import"==d&&";"==k&&b.stack.pop():b.stack.push("propertyValue");return c},indent:function(a,b){var d=a.stack.length;return/^\}/.test(b)&&(d-="propertyValue"==a.stack[a.stack.length-1]?2:1),a.baseIndent+d*c},electricChars:"}",blockCommentStart:"/*",blockCommentEnd:"*/"}}),function(){function a(a){for(var b={},c=0;a.length>c;++c)b[a[c]]=!0;return b}function g(a,b){for(var d,c=!1;null!=(d=a.next());){if(c&&"/"==d){b.tokenize=null;break}c="*"==d}return["comment","comment"]}var b=a(["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"]),c=a(["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid"]),d=a(["align-content","align-items","align-self","alignment-adjust","alignment-baseline","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backface-visibility","background","background-attachment","background-clip","background-color","background-image","background-origin","background-position","background-repeat","background-size","baseline-shift","binding","bleed","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","font","font-feature-settings","font-family","font-kerning","font-language-override","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-weight","grid-cell","grid-column","grid-column-align","grid-column-sizing","grid-column-span","grid-columns","grid-flow","grid-row","grid-row-align","grid-row-sizing","grid-row-span","grid-rows","grid-template","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","justify-content","left","letter-spacing","line-break","line-height","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marker-offset","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","max-height","max-width","min-height","min-width","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","play-during","position","presentation-level","punctuation-trim","quotes","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotation","rotation-point","ruby-align","ruby-overhang","ruby-position","ruby-span","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-outline","text-shadow","text-space-collapse","text-transform","text-underline-position","text-wrap","top","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","unicode-bidi","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","word-break","word-spacing","word-wrap","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-profile","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","kerning","text-anchor","writing-mode"]),e=a(["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"]),f=a(["above","absolute","activeborder","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","auto","avoid","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","bold","bolder","border","border-box","both","bottom","break-all","break-word","button","button-bevel","buttonface","buttonhighlight","buttonshadow","buttontext","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","compact","condensed","contain","content","content-box","context-menu","continuous","copy","cover","crop","cross","crosshair","currentcolor","cursive","dashed","decimal","decimal-leading-zero","default","default-button","destination-atop","destination-in","destination-out","destination-over","devanagari","disc","discard","document","dot-dash","dot-dot-dash","dotted","double","down","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ew-resize","expanded","extra-condensed","extra-expanded","fantasy","fast","fill","fixed","flat","footnotes","forwards","from","geometricPrecision","georgian","graytext","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hebrew","help","hidden","hide","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-table","inset","inside","intrinsic","invert","italic","justify","kannada","katakana","katakana-iroha","khmer","landscape","lao","large","larger","left","level","lighter","line-through","linear","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","malayalam","match","media-controls-background","media-current-time-display","media-fullscreen-button","media-mute-button","media-play-button","media-return-to-realtime-button","media-rewind-button","media-seek-back-button","media-seek-forward-button","media-slider","media-sliderthumb","media-time-remaining-display","media-volume-slider","media-volume-slider-container","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menulist-text","menulist-textfield","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","nw-resize","nwse-resize","oblique","octal","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","overlay","overline","padding","padding-box","painted","paused","persian","plus-darker","plus-lighter","pointer","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radio","read-only","read-write","read-write-plaintext-only","relative","repeat","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","round","row-resize","rtl","run-in","running","s-resize","sans-serif","scroll","scrollbar","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","semi-condensed","semi-expanded","separate","serif","show","sidama","single","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","solid","somali","source-atop","source-in","source-out","source-over","space","square","square-button","start","static","status-bar","stretch","stroke","sub","subpixel-antialiased","super","sw-resize","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","transparent","ultra-condensed","ultra-expanded","underline","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","vertical","vertical-text","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","x-large","x-small","xor","xx-large","xx-small"]);CodeMirror.defineMIME("text/css",{atMediaTypes:b,atMediaFeatures:c,propertyKeywords:d,colorKeywords:e,valueKeywords:f,hooks:{"<":function(a,b){function c(a,b){for(var d,c=0;null!=(d=a.next());){if(c>=2&&">"==d){b.tokenize=null;break}c="-"==d?c+1:0}return["comment","comment"]}return a.eat("!")?(b.tokenize=c,c(a,b)):void 0},"/":function(a,b){return a.eat("*")?(b.tokenize=g,g(a,b)):!1}},name:"css-base"}),CodeMirror.defineMIME("text/x-scss",{atMediaTypes:b,atMediaFeatures:c,propertyKeywords:d,colorKeywords:e,valueKeywords:f,allowNested:!0,hooks:{$:function(a){return a.match(/^[\w-]+/),":"==a.peek()?["variable","variable-definition"]:["variable","variable"]},"/":function(a,b){return a.eat("/")?(a.skipToEnd(),["comment","comment"]):a.eat("*")?(b.tokenize=g,g(a,b)):["operator","operator"]},"#":function(a){return a.eat("{")?["operator","interpolation"]:(a.eatWhile(/[\w\\\-]/),["atom","hash"])}},name:"css-base"})}(),CodeMirror.defineMode("htmlmixed",function(a,b){function i(a,b){var f=b.htmlState.tagName,g=c.token(a,b.htmlState);if("script"==f&&/\btag\b/.test(g)&&">"==a.current()){var h=a.string.slice(Math.max(0,a.pos-100),a.pos).match(/\btype\s*=\s*("[^"]+"|'[^']+'|\S+)[^<]*$/i);h=h?h[1]:"",h&&/[\"\']/.test(h.charAt(0))&&(h=h.slice(1,h.length-1));for(var i=0;e.length>i;++i){var j=e[i];if("string"==typeof j.matches?h==j.matches:j.matches.test(h)){j.mode&&(b.token=k,b.localMode=j.mode,b.localState=j.mode.startState&&j.mode.startState(c.indent(b.htmlState,"")));break}}}else"style"==f&&/\btag\b/.test(g)&&">"==a.current()&&(b.token=l,b.localMode=d,b.localState=d.startState(c.indent(b.htmlState,"")));return g}function j(a,b,c){var f,d=a.current(),e=d.search(b);return e>-1?a.backUp(d.length-e):(f=d.match(/<\/?$/))&&(a.backUp(d.length),a.match(b,!1)||a.match(d[0])),c}function k(a,b){return a.match(/^<\/\s*script\s*>/i,!1)?(b.token=i,b.localState=b.localMode=null,i(a,b)):j(a,/<\/\s*script\s*>/,b.localMode.token(a,b.localState))}function l(a,b){return a.match(/^<\/\s*style\s*>/i,!1)?(b.token=i,b.localState=b.localMode=null,i(a,b)):j(a,/<\/\s*style\s*>/,d.token(a,b.localState))}var c=CodeMirror.getMode(a,{name:"xml",htmlMode:!0}),d=CodeMirror.getMode(a,"css"),e=[],f=b&&b.scriptTypes;if(e.push({matches:/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^$/i,mode:CodeMirror.getMode(a,"javascript")}),f)for(var g=0;f.length>g;++g){var h=f[g];e.push({matches:h.matches,mode:h.mode&&CodeMirror.getMode(a,h.mode)})}return e.push({matches:/./,mode:CodeMirror.getMode(a,"text/plain")}),{startState:function(){var a=c.startState();return{token:i,localMode:null,localState:null,htmlState:a}},copyState:function(a){if(a.localState)var b=CodeMirror.copyState(a.localMode,a.localState);return{token:a.token,localMode:a.localMode,localState:b,htmlState:CodeMirror.copyState(c,a.htmlState)}},token:function(a,b){return b.token(a,b)},indent:function(a,b){return!a.localMode||/^\s*<\//.test(b)?c.indent(a.htmlState,b):a.localMode.indent?a.localMode.indent(a.localState,b):CodeMirror.Pass},electricChars:"/{}:",innerMode:function(a){return{state:a.localState||a.htmlState,mode:a.localMode||c}}}},"xml","javascript","css"),CodeMirror.defineMIME("text/html","htmlmixed"),CodeMirror.defineMode("javascript",function(a,b){function h(a,b,c){return b.tokenize=c,c(a,b)}function i(a,b){for(var d,c=!1;null!=(d=a.next());){if(d==b&&!c)return!1;c=!c&&"\\"==d}return c}function l(a,b,c){return j=a,k=c,b}function m(a,b){var c=a.next();if('"'==c||"'"==c)return h(a,b,n(c));if(/[\[\]{}\(\),;\:\.]/.test(c))return l(c);if("0"==c&&a.eat(/x/i))return a.eatWhile(/[\da-f]/i),l("number","number");if(/\d/.test(c)||"-"==c&&a.eat(/\d/))return a.match(/^\d*(?:\.\d*)?(?:[eE][+\-]?\d+)?/),l("number","number");if("/"==c)return a.eat("*")?h(a,b,o):a.eat("/")?(a.skipToEnd(),l("comment","comment")):"operator"==b.lastType||"keyword c"==b.lastType||/^[\[{}\(,;:]$/.test(b.lastType)?(i(a,"/"),a.eatWhile(/[gimy]/),l("regexp","string-2")):(a.eatWhile(g),l("operator",null,a.current()));if("#"==c)return a.skipToEnd(),l("error","error");if(g.test(c))return a.eatWhile(g),l("operator",null,a.current());a.eatWhile(/[\w\$_]/);var d=a.current(),e=f.propertyIsEnumerable(d)&&f[d];return e&&"."!=b.lastType?l(e.type,e.style,d):l("variable","variable",d)}function n(a){return function(b,c){return i(b,a)||(c.tokenize=m),l("string","string")}}function o(a,b){for(var d,c=!1;d=a.next();){if("/"==d&&c){b.tokenize=m;break}c="*"==d}return l("comment","comment")}function q(a,b,c,d,e,f){this.indented=a,this.column=b,this.type=c,this.prev=e,this.info=f,null!=d&&(this.align=d)}function r(a,b){for(var c=a.localVars;c;c=c.next)if(c.name==b)return!0}function s(a,b,c,e,f){var g=a.cc;for(t.state=a,t.stream=f,t.marked=null,t.cc=g,a.lexical.hasOwnProperty("align")||(a.lexical.align=!0);;){var h=g.length?g.pop():d?E:D;if(h(c,e)){for(;g.length&&g[g.length-1].lex;)g.pop()();return t.marked?t.marked:"variable"==c&&r(a,e)?"variable-2":b}}}function u(){for(var a=arguments.length-1;a>=0;a--)t.cc.push(arguments[a])}function v(){return u.apply(null,arguments),!0}function w(a){function b(b){for(var c=b;c;c=c.next)if(c.name==a)return!0;return!1}var c=t.state;if(c.context){if(t.marked="def",b(c.localVars))return;c.localVars={name:a,next:c.localVars}}else{if(b(c.globalVars))return;c.globalVars={name:a,next:c.globalVars}}}function y(){t.state.context={prev:t.state.context,vars:t.state.localVars},t.state.localVars=x}function z(){t.state.localVars=t.state.context.vars,t.state.context=t.state.context.prev}function A(a,b){var c=function(){var c=t.state;c.lexical=new q(c.indented,t.stream.column(),a,null,c.lexical,b)};return c.lex=!0,c}function B(){var a=t.state;a.lexical.prev&&(")"==a.lexical.type&&(a.indented=a.lexical.indented),a.lexical=a.lexical.prev)}function C(a){return function(b){return b==a?v():";"==a?u():v(arguments.callee)}}function D(a){return"var"==a?v(A("vardef"),S,C(";"),B):"keyword a"==a?v(A("form"),E,D,B):"keyword b"==a?v(A("form"),D,B):"{"==a?v(A("}"),P,B):";"==a?v():"if"==a?v(A("form"),E,D,B,U(t.state.indented)):"function"==a?v(Z):"for"==a?v(A("form"),C("("),A(")"),V,C(")"),B,D,B):"variable"==a?v(A("stat"),K):"switch"==a?v(A("form"),E,A("}","switch"),C("{"),P,B,B):"case"==a?v(E,C(":")):"default"==a?v(C(":")):"catch"==a?v(A("form"),y,C("("),$,C(")"),D,B,z):u(A("stat"),E,C(";"),B)}function E(a){return G(a,I)}function F(a){return G(a,J)}function G(a,b){return p.hasOwnProperty(a)?v(b):"function"==a?v(Z):"keyword c"==a?v(H):"("==a?v(A(")"),H,C(")"),B,b):"operator"==a?v(E):"["==a?v(A("]"),O(F,"]"),B,b):"{"==a?v(A("}"),O(M,"}"),B,b):v()}function H(a){return a.match(/[;\}\)\],]/)?u():u(E)}function I(a,b){return","==a?u():J(a,b,I)}function J(a,b,c){return c||(c=J),"operator"==a?/\+\+|--/.test(b)?v(c):"?"==b?v(E,C(":"),E):v(E):";"!=a?"("==a?v(A(")","call"),O(F,")"),B,c):"."==a?v(L,c):"["==a?v(A("]"),E,C("]"),B,c):void 0:void 0}function K(a){return":"==a?v(B,D):u(I,C(";"),B)}function L(a){return"variable"==a?(t.marked="property",v()):void 0}function M(a,b){if("variable"==a){if(t.marked="property","get"==b||"set"==b)return v(N)}else("number"==a||"string"==a)&&(t.marked=a+" property");return p.hasOwnProperty(a)?v(C(":"),F):void 0}function N(a){return":"==a?v(E):"variable"!=a?v(C(":"),E):(t.marked="property",v(Z))}function O(a,b){function c(d){if(","==d){var e=t.state.lexical;return"call"==e.info&&(e.pos=(e.pos||0)+1),v(a,c)}return d==b?v():v(C(b))}return function(d){return d==b?v():u(a,c)}}function P(a){return"}"==a?v():u(D,P)}function Q(a){return":"==a?v(R):u()}function R(a){return"variable"==a?(t.marked="variable-3",v()):u()}function S(a,b){return"variable"==a?(w(b),e?v(Q,T):v(T)):u()}function T(a,b){return"="==b?v(F,T):","==a?v(S):void 0}function U(a){return function(b,c){return"keyword b"==b&&"else"==c?(t.state.lexical=new q(a,0,"form",null,t.state.lexical),v(D,B)):u()}}function V(a){return"var"==a?v(S,C(";"),X):";"==a?v(X):"variable"==a?v(W):u(E,C(";"),X)}function W(a,b){return"in"==b?v(E):v(I,X)}function X(a,b){return";"==a?v(Y):"in"==b?v(E):u(E,C(";"),Y)}function Y(a){")"!=a&&v(E)}function Z(a,b){return"variable"==a?(w(b),v(Z)):"("==a?v(A(")"),y,O($,")"),B,D,z):void 0}function $(a,b){return"variable"==a?(w(b),e?v(Q):v()):void 0}var j,k,c=a.indentUnit,d=b.json,e=b.typescript,f=function(){function a(a){return{type:a,style:"keyword"}}var b=a("keyword a"),c=a("keyword b"),d=a("keyword c"),f=a("operator"),g={type:"atom",style:"atom"},h={"if":a("if"),"while":b,"with":b,"else":c,"do":c,"try":c,"finally":c,"return":d,"break":d,"continue":d,"new":d,"delete":d,"throw":d,"var":a("var"),"const":a("var"),let:a("var"),"function":a("function"),"catch":a("catch"),"for":a("for"),"switch":a("switch"),"case":a("case"),"default":a("default"),"in":f,"typeof":f,"instanceof":f,"true":g,"false":g,"null":g,undefined:g,NaN:g,Infinity:g,"this":a("this")};
if(e){var i={type:"variable",style:"variable-3"},j={"interface":a("interface"),"class":a("class"),"extends":a("extends"),constructor:a("constructor"),"public":a("public"),"private":a("private"),"protected":a("protected"),"static":a("static"),"super":a("super"),string:i,number:i,bool:i,any:i};for(var k in j)h[k]=j[k]}return h}(),g=/[+\-*&%=<>!?|~^]/,p={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,"this":!0},t={state:null,column:null,marked:null,cc:null},x={name:"this",next:{name:"arguments"}};return B.lex=!0,{startState:function(a){return{tokenize:m,lastType:null,cc:[],lexical:new q((a||0)-c,0,"block",!1),localVars:b.localVars,globalVars:b.globalVars,context:b.localVars&&{vars:b.localVars},indented:0}},token:function(a,b){if(a.sol()&&(b.lexical.hasOwnProperty("align")||(b.lexical.align=!1),b.indented=a.indentation()),b.tokenize!=o&&a.eatSpace())return null;var c=b.tokenize(a,b);return"comment"==j?c:(b.lastType="operator"!=j||"++"!=k&&"--"!=k?j:"incdec",s(b,c,j,k,a))},indent:function(a,d){if(a.tokenize==o)return CodeMirror.Pass;if(a.tokenize!=m)return 0;var e=d&&d.charAt(0),f=a.lexical;"stat"==f.type&&"}"==e&&(f=f.prev);var g=f.type,h=e==g;return null!=b.statementIndent&&(")"==g&&f.prev&&"stat"==f.prev.type&&(f=f.prev),"stat"==f.type)?f.indented+b.statementIndent:"vardef"==g?f.indented+("operator"==a.lastType||","==a.lastType?4:0):"form"==g&&"{"==e?f.indented:"form"==g?f.indented+c:"stat"==g?f.indented+("operator"==a.lastType||","==a.lastType?c:0):"switch"!=f.info||h?f.align?f.column+(h?0:1):f.indented+(h?0:c):f.indented+(/^(?:case|default)\b/.test(d)?c:2*c)},electricChars:":{}",blockCommentStart:d?null:"/*",blockCommentEnd:d?null:"*/",lineComment:d?null:"//",jsonMode:d}}),CodeMirror.defineMIME("text/javascript","javascript"),CodeMirror.defineMIME("text/ecmascript","javascript"),CodeMirror.defineMIME("application/javascript","javascript"),CodeMirror.defineMIME("application/ecmascript","javascript"),CodeMirror.defineMIME("application/json",{name:"javascript",json:!0}),CodeMirror.defineMIME("application/x-json",{name:"javascript",json:!0}),CodeMirror.defineMIME("text/typescript",{name:"javascript",typescript:!0}),CodeMirror.defineMIME("application/typescript",{name:"javascript",typescript:!0}),CodeMirror.defineMode("xml",function(a,b){function i(a,b){function c(c){return b.tokenize=c,c(a,b)}var d=a.next();if("<"==d){if(a.eat("!"))return a.eat("[")?a.match("CDATA[")?c(l("atom","]]>")):null:a.match("--")?c(l("comment","-->")):a.match("DOCTYPE",!0,!0)?(a.eatWhile(/[\w\._\-]/),c(m(1))):null;if(a.eat("?"))return a.eatWhile(/[\w\._\-]/),b.tokenize=l("meta","?>"),"meta";var e=a.eat("/");g="";for(var f;f=a.eat(/[^\s\u00a0=<>\"\'\/?]/);)g+=f;return g?(h=e?"closeTag":"openTag",b.tokenize=j,"tag"):"error"}if("&"==d){var i;return i=a.eat("#")?a.eat("x")?a.eatWhile(/[a-fA-F\d]/)&&a.eat(";"):a.eatWhile(/[\d]/)&&a.eat(";"):a.eatWhile(/[\w\.\-:]/)&&a.eat(";"),i?"atom":"error"}return a.eatWhile(/[^&<]/),null}function j(a,b){var c=a.next();return">"==c||"/"==c&&a.eat(">")?(b.tokenize=i,h=">"==c?"endTag":"selfcloseTag","tag"):"="==c?(h="equals",null):/[\'\"]/.test(c)?(b.tokenize=k(c),b.tokenize(a,b)):(a.eatWhile(/[^\s\u00a0=<>\"\']/),"word")}function k(a){return function(b,c){for(;!b.eol();)if(b.next()==a){c.tokenize=j;break}return"string"}}function l(a,b){return function(c,d){for(;!c.eol();){if(c.match(b)){d.tokenize=i;break}c.next()}return a}}function m(a){return function(b,c){for(var d;null!=(d=b.next());){if("<"==d)return c.tokenize=m(a+1),c.tokenize(b,c);if(">"==d){if(1==a){c.tokenize=i;break}return c.tokenize=m(a-1),c.tokenize(b,c)}}return"meta"}}function q(){for(var a=arguments.length-1;a>=0;a--)n.cc.push(arguments[a])}function r(){return q.apply(null,arguments),!0}function s(a,b){var c=e.doNotIndent.hasOwnProperty(a)||n.context&&n.context.noIndent;n.context={prev:n.context,tagName:a,indent:n.indented,startOfLine:b,noIndent:c}}function t(){n.context&&(n.context=n.context.prev)}function u(a){if("openTag"==a)return n.tagName=g,n.tagStart=o.column(),r(y,v(n.startOfLine));if("closeTag"==a){var b=!1;return n.context?n.context.tagName!=g&&(e.implicitlyClosed.hasOwnProperty(n.context.tagName.toLowerCase())&&t(),b=!n.context||n.context.tagName!=g):b=!0,b&&(p="error"),r(w(b))}return r()}function v(a){return function(b){var c=n.tagName;return n.tagName=n.tagStart=null,"selfcloseTag"==b||"endTag"==b&&e.autoSelfClosers.hasOwnProperty(c.toLowerCase())?(x(c.toLowerCase()),r()):"endTag"==b?(x(c.toLowerCase()),s(c,a),r()):r()}}function w(a){return function(b){return a&&(p="error"),"endTag"==b?(t(),r()):(p="error",r(arguments.callee))}}function x(a){for(var b;;){if(!n.context)return;if(b=n.context.tagName.toLowerCase(),!e.contextGrabbers.hasOwnProperty(b)||!e.contextGrabbers[b].hasOwnProperty(a))return;t()}}function y(a){return"word"==a?(p="attribute",r(z,y)):"endTag"==a||"selfcloseTag"==a?q():(p="error",r(y))}function z(a){return"equals"==a?r(A,y):(e.allowMissing?"word"==a&&(p="attribute"):p="error","endTag"==a||"selfcloseTag"==a?q():r())}function A(a){return"string"==a?r(B):"word"==a&&e.allowUnquoted?(p="string",r()):(p="error","endTag"==a||"selfCloseTag"==a?q():r())}function B(a){return"string"==a?r(B):q()}var g,h,n,o,p,c=a.indentUnit,d=b.multilineTagIndentFactor||1,e=b.htmlMode?{autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0}:{autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1},f=b.alignCDATA;return{startState:function(){return{tokenize:i,cc:[],indented:0,startOfLine:!0,tagName:null,tagStart:null,context:null}},token:function(a,b){if(!b.tagName&&a.sol()&&(b.startOfLine=!0,b.indented=a.indentation()),a.eatSpace())return null;p=h=g=null;var c=b.tokenize(a,b);if(b.type=h,(c||h)&&"comment"!=c)for(n=b,o=a;;){var d=b.cc.pop()||u;if(d(h||c))break}return b.startOfLine=!1,p||c},indent:function(a,b,e){var g=a.context;if(a.tokenize!=j&&a.tokenize!=i||g&&g.noIndent)return e?e.match(/^(\s*)/)[0].length:0;if(a.tagName)return a.tagStart+c*d;if(f&&/<!\[CDATA\[/.test(b))return 0;for(g&&/^<\//.test(b)&&(g=g.prev);g&&!g.startOfLine;)g=g.prev;return g?g.indent+c:0},electricChars:"/",blockCommentStart:"<!--",blockCommentEnd:"-->",configuration:b.htmlMode?"html":"xml"}}),CodeMirror.defineMIME("text/xml","xml"),CodeMirror.defineMIME("application/xml","xml"),CodeMirror.mimeModes.hasOwnProperty("text/html")||CodeMirror.defineMIME("text/html",{name:"xml",htmlMode:!0}),function(){function c(c,e){var f=c.getCursor(),g=c.getTokenAt(f),h=CodeMirror.innerMode(c.getMode(),g.state),i=h.state;if("xml"!=h.mode.name)return CodeMirror.Pass;var j=c.getOption("autoCloseTags"),k="html"==h.mode.configuration,l="object"==typeof j&&j.dontCloseTags||k&&a,m="object"==typeof j&&j.indentTags||k&&b;if(">"==e&&i.tagName){var n=i.tagName;g.end>f.ch&&(n=n.slice(0,n.length-g.end+f.ch));var o=n.toLowerCase();if("tag"==g.type&&"closeTag"==i.type||g.string.indexOf("/")==g.string.length-1||l&&d(l,o)>-1)return CodeMirror.Pass;var p=m&&d(m,o)>-1,q=p?CodeMirror.Pos(f.line+1,0):CodeMirror.Pos(f.line,f.ch+1);return c.replaceSelection(">"+(p?"\n\n":"")+"</"+n+">",{head:q,anchor:q}),p&&(c.indentLine(f.line+1),c.indentLine(f.line+2)),void 0}if("/"==e&&"<"==g.string){var n=i.context&&i.context.tagName;return n&&c.replaceSelection("/"+n+">","end"),void 0}return CodeMirror.Pass}function d(a,b){if(a.indexOf)return a.indexOf(b);for(var c=0,d=a.length;d>c;++c)if(a[c]==b)return c;return-1}CodeMirror.defineOption("autoCloseTags",!1,function(a,b,d){if(!b||d!=CodeMirror.Init&&d)!b&&d!=CodeMirror.Init&&d&&a.removeKeyMap("autoCloseTags");else{var e={name:"autoCloseTags"};("object"!=typeof b||b.whenClosing)&&(e["'/'"]=function(a){return c(a,"/")}),("object"!=typeof b||b.whenOpening)&&(e["'>'"]=function(a){return c(a,">")}),a.addKeyMap(e)}});var a=["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"],b=["applet","blockquote","body","button","div","dl","fieldset","form","frameset","h1","h2","h3","h4","h5","h6","head","html","iframe","layer","legend","object","ol","p","select","table","ul"]}(),CodeMirror.showHint=function(a,b,c){function g(){return a.somethingSelected()?void 0:c.async?(b(a,j,c),void 0):j(b(a,c))}function h(a){return"string"==typeof a?a:a.text}function i(a,b,c){c.hint?c.hint(a,b,c):a.replaceRange(h(c),b.from,b.to)}function j(b){function A(a){if(a=Math.max(0,Math.min(a,j.length-1)),l!=a){var c=k.childNodes[l];c.className=c.className.replace(" CodeMirror-hint-active",""),c=k.childNodes[l=a],c.className+=" CodeMirror-hint-active",c.offsetTop<k.scrollTop?k.scrollTop=c.offsetTop-3:c.offsetTop+c.offsetHeight>k.scrollTop+k.clientHeight&&(k.scrollTop=c.offsetTop+c.offsetHeight-k.clientHeight+3),CodeMirror.signal(b,"select",j[l],c)}}function B(){return Math.floor(k.clientHeight/k.firstChild.offsetHeight)||1}function H(){G=setTimeout(N,100)}function I(){clearTimeout(G)}function K(){var b=a.getScrollInfo(),c=a.getWrapperElement().getBoundingClientRect(),d=s+J.top-b.top,e=d;return t||(e+=k.offsetHeight),c.top>=e||e>=c.bottom?N():(k.style.top=d+"px",k.style.left=r+J.left-b.left+"px",void 0)}function N(c){L||(L=!0,clearTimeout(M),k.parentNode.removeChild(k),a.removeKeyMap(C),a.off("cursorActivity",R),a.off("blur",H),a.off("focus",I),a.off("scroll",K),c!==!0&&CodeMirror.signal(b,"close"))}function O(){i(a,b,j[l]),N()}function R(){clearTimeout(M);var b=a.getCursor(),c=a.getLine(b.line);b.line!=P.line||c.length-b.ch!=Q-P.ch||d>b.ch||a.somethingSelected()||b.ch&&f.test(c.charAt(b.ch-1))?N():M=setTimeout(function(){N(!0),e=!0,g()},70)}if(b&&b.list.length){var j=b.list;if(!e&&c.completeSingle!==!1&&1==j.length)return i(a,b,j[0]),CodeMirror.signal(b,"close"),!0;var k=document.createElement("ul"),l=0;k.className="CodeMirror-hints";for(var m=0;j.length>m;++m){var n=k.appendChild(document.createElement("li")),o=j[m],p="CodeMirror-hint"+(m?"":" CodeMirror-hint-active");null!=o.className&&(p=o.className+" "+p),n.className=p,o.render?o.render(n,b,o):n.appendChild(document.createTextNode(o.displayText||h(o))),n.hintId=m}var q=a.cursorCoords(c.alignWithWord!==!1?b.from:null),r=q.left,s=q.bottom,t=!0;k.style.left=r+"px",k.style.top=s+"px",document.body.appendChild(k),CodeMirror.signal(b,"shown");var u=window.innerWidth||Math.max(document.body.offsetWidth,document.documentElement.offsetWidth),v=window.innerHeight||Math.max(document.body.offsetHeight,document.documentElement.offsetHeight),w=k.getBoundingClientRect(),x=w.right-u,y=w.bottom-v;if(x>0&&(w.right-w.left>u&&(k.style.width=u-5+"px",x-=w.right-w.left-u),k.style.left=(r=q.left-x)+"px"),y>0){var z=w.bottom-w.top;w.top-(q.bottom-q.top)-z>0?(y=z+(q.bottom-q.top),t=!1):z>v&&(k.style.height=v-5+"px",y-=z-v),k.style.top=(s=q.bottom-y)+"px"}var C,D={Up:function(){A(l-1)},Down:function(){A(l+1)},PageUp:function(){A(l-B())},PageDown:function(){A(l+B())},Home:function(){A(0)},End:function(){A(j.length-1)},Enter:O,Tab:O,Esc:N};if(c.customKeys){C={};for(var E in c.customKeys)if(c.customKeys.hasOwnProperty(E)){var F=c.customKeys[E];D.hasOwnProperty(F)&&(F=D[F]),C[E]=F}}else C=D;a.addKeyMap(C),a.on("cursorActivity",R);var G;a.on("blur",H),a.on("focus",I);var J=a.getScrollInfo();a.on("scroll",K),CodeMirror.on(k,"dblclick",function(a){var b=a.target||a.srcElement;null!=b.hintId&&(l=b.hintId,O())}),CodeMirror.on(k,"click",function(a){var b=a.target||a.srcElement;null!=b.hintId&&A(b.hintId)}),CodeMirror.on(k,"mousedown",function(){setTimeout(function(){a.focus()},20)});var M,M,L=!1,P=a.getCursor(),Q=a.getLine(P.line).length;return CodeMirror.signal(b,"select",j[0],k.firstChild),!0}}c||(c={});var d=a.getCursor().ch,e=!1,f=c.closeCharacters||/[\s()\[\]{};:]/;return g()},function(){function a(a,c,e){var f=a.getCursor(),g=e(a,f),h=[],i=0,j=0,k=0,l={line:f.line,ch:f.ch},m={line:f.line,ch:f.ch},n=!0,o=a.getRange({line:0,ch:0},f),p=o.lastIndexOf("<"),q=o.lastIndexOf(">"),r=g.string.replace("<","");if(p>q){var s=a.getRange({line:f.line,ch:f.ch-1},f);if("<"==s){for(i=0;c.length>i;i++)h.push(c[i].tag);l.ch=g.start+1}else{var t=0,u=function(b,c,d){if(t++,!(t>50)){if(b.type==c)return b;d.ch=b.start;var e=a.getTokenAt(d);return u(e,c,d)}},v=u(g,"tag",{line:f.line,ch:f.ch}),w=v.string.substring(1);if(null===g.type&&""===g.string.trim()){for(i=0;c.length>i;i++)if(c[i].tag==w){for(j=0;c[i].attr.length>j;j++)h.push(c[i].attr[j].key+'="" ');for(k=0;d.length>k;k++)h.push(d[k].key+'="" ')}}else if("string"==g.type){r=r.substring(1,r.length-1);var x=u(g,"attribute",{line:f.line,ch:f.ch}),y=x.string;for(i=0;c.length>i;i++)if(c[i].tag==w){for(j=0;c[i].attr.length>j;j++)if(c[i].attr[j].key==y)for(k=0;c[i].attr[j].values.length>k;k++)h.push(c[i].attr[j].values[k]);for(j=0;d.length>j;j++)if(d[j].key==y)for(k=0;d[j].values.length>k;k++)h.push(d[j].values[k])}l.ch=g.start+1}else if("attribute"==g.type){for(i=0;c.length>i;i++)if(c[i].tag==w){for(j=0;c[i].attr.length>j;j++)h.push(c[i].attr[j].key+'="" ');for(k=0;d.length>k;k++)h.push(d[k].key+'="" ')}l.ch=g.start}else if("tag"==g.type){for(i=0;c.length>i;i++)h.push(c[i].tag);l.ch=g.start+1}}}else{for(i=0;c.length>i;i++)h.push("<"+c[i].tag);r=("<"+r).trim(),l.ch=g.start}return n===!0&&""===r.trim()&&(n=!1),n&&(h=b(r,h)),{list:h,from:l,to:m}}var b=function(a,b){var c=[],d=0;for(d=0;b.length>d;d++)b[d].substring(0,a.length)==a&&c.push(b[d]);return c},c=[{tag:"!DOCTYPE",attr:[]},{tag:"a",attr:[{key:"href",values:["#"]},{key:"target",values:["_blank","_self","_top","_parent"]},{key:"ping",values:[""]},{key:"media",values:["#"]},{key:"hreflang",values:["en","es"]},{key:"type",values:[]}]},{tag:"abbr",attr:[]},{tag:"acronym",attr:[]},{tag:"address",attr:[]},{tag:"applet",attr:[]},{tag:"area",attr:[{key:"alt",values:[""]},{key:"coords",values:["rect: left, top, right, bottom","circle: center-x, center-y, radius","poly: x1, y1, x2, y2, ..."]},{key:"shape",values:["default","rect","circle","poly"]},{key:"href",values:["#"]},{key:"target",values:["#"]},{key:"ping",values:[]},{key:"media",values:[]},{key:"hreflang",values:[]},{key:"type",values:[]}]},{tag:"article",attr:[]},{tag:"aside",attr:[]},{tag:"audio",attr:[{key:"src",values:[]},{key:"crossorigin",values:["anonymous","use-credentials"]},{key:"preload",values:["none","metadata","auto"]},{key:"autoplay",values:["","autoplay"]},{key:"mediagroup",values:[]},{key:"loop",values:["","loop"]},{key:"controls",values:["","controls"]}]},{tag:"b",attr:[]},{tag:"base",attr:[{key:"href",values:["#"]},{key:"target",values:["_blank","_self","_top","_parent"]}]},{tag:"basefont",attr:[]},{tag:"bdi",attr:[]},{tag:"bdo",attr:[]},{tag:"big",attr:[]},{tag:"blockquote",attr:[{key:"cite",values:["http://"]}]},{tag:"body",attr:[]},{tag:"br",attr:[]},{tag:"button",attr:[{key:"autofocus",values:["","autofocus"]},{key:"disabled",values:["","disabled"]},{key:"form",values:[]},{key:"formaction",values:[]},{key:"formenctype",values:["application/x-www-form-urlencoded","multipart/form-data","text/plain"]},{key:"formmethod",values:["get","post","put","delete"]},{key:"formnovalidate",values:["","novalidate"]},{key:"formtarget",values:["_blank","_self","_top","_parent"]},{key:"name",values:[]},{key:"type",values:["submit","reset","button"]},{key:"value",values:[]}]},{tag:"canvas",attr:[{key:"width",values:[]},{key:"height",values:[]}]},{tag:"caption",attr:[]},{tag:"center",attr:[]},{tag:"cite",attr:[]},{tag:"code",attr:[]},{tag:"col",attr:[{key:"span",values:[]}]},{tag:"colgroup",attr:[{key:"span",values:[]}]},{tag:"command",attr:[{key:"type",values:["command","checkbox","radio"]},{key:"label",values:[]},{key:"icon",values:[]},{key:"disabled",values:["","disabled"]},{key:"checked",values:["","checked"]},{key:"radiogroup",values:[]},{key:"command",values:[]},{key:"title",values:[]}]},{tag:"data",attr:[{key:"value",values:[]}]},{tag:"datagrid",attr:[{key:"disabled",values:["","disabled"]},{key:"multiple",values:["","multiple"]}]},{tag:"datalist",attr:[{key:"data",values:[]}]},{tag:"dd",attr:[]},{tag:"del",attr:[{key:"cite",values:[]},{key:"datetime",values:[]}]},{tag:"details",attr:[{key:"open",values:["","open"]}]},{tag:"dfn",attr:[]},{tag:"dir",attr:[]},{tag:"div",attr:[{key:"id",values:[]},{key:"class",values:[]},{key:"style",values:[]}]},{tag:"dl",attr:[]},{tag:"dt",attr:[]},{tag:"em",attr:[]},{tag:"embed",attr:[{key:"src",values:[]},{key:"type",values:[]},{key:"width",values:[]},{key:"height",values:[]}]},{tag:"eventsource",attr:[{key:"src",values:[]}]},{tag:"fieldset",attr:[{key:"disabled",values:["","disabled"]},{key:"form",values:[]},{key:"name",values:[]}]},{tag:"figcaption",attr:[]},{tag:"figure",attr:[]},{tag:"font",attr:[]},{tag:"footer",attr:[]},{tag:"form",attr:[{key:"accept-charset",values:["UNKNOWN","utf-8"]},{key:"action",values:[]},{key:"autocomplete",values:["on","off"]},{key:"enctype",values:["application/x-www-form-urlencoded","multipart/form-data","text/plain"]},{key:"method",values:["get","post","put","delete","dialog"]},{key:"name",values:[]},{key:"novalidate",values:["","novalidate"]},{key:"target",values:["_blank","_self","_top","_parent"]}]},{tag:"frame",attr:[]},{tag:"frameset",attr:[]},{tag:"h1",attr:[]},{tag:"h2",attr:[]},{tag:"h3",attr:[]},{tag:"h4",attr:[]},{tag:"h5",attr:[]},{tag:"h6",attr:[]},{tag:"head",attr:[]},{tag:"header",attr:[]},{tag:"hgroup",attr:[]},{tag:"hr",attr:[]},{tag:"html",attr:[{key:"manifest",values:[]}]},{tag:"i",attr:[]},{tag:"iframe",attr:[{key:"src",values:[]},{key:"srcdoc",values:[]},{key:"name",values:[]},{key:"sandbox",values:["allow-top-navigation","allow-same-origin","allow-forms","allow-scripts"]},{key:"seamless",values:["","seamless"]},{key:"width",values:[]},{key:"height",values:[]}]},{tag:"img",attr:[{key:"alt",values:[]},{key:"src",values:[]},{key:"crossorigin",values:["anonymous","use-credentials"]},{key:"ismap",values:[]},{key:"usemap",values:[]},{key:"width",values:[]},{key:"height",values:[]}]},{tag:"input",attr:[{key:"accept",values:["audio/*","video/*","image/*"]},{key:"alt",values:[]},{key:"autocomplete",values:["on","off"]},{key:"autofocus",values:["","autofocus"]},{key:"checked",values:["","checked"]},{key:"disabled",values:["","disabled"]},{key:"dirname",values:[]},{key:"form",values:[]},{key:"formaction",values:[]},{key:"formenctype",values:["application/x-www-form-urlencoded","multipart/form-data","text/plain"]},{key:"formmethod",values:["get","post","put","delete"]},{key:"formnovalidate",values:["","novalidate"]},{key:"formtarget",values:["_blank","_self","_top","_parent"]},{key:"height",values:[]},{key:"list",values:[]},{key:"max",values:[]},{key:"maxlength",values:[]},{key:"min",values:[]},{key:"multiple",values:["","multiple"]},{key:"name",values:[]},{key:"pattern",values:[]},{key:"placeholder",values:[]},{key:"readonly",values:["","readonly"]},{key:"required",values:["","required"]},{key:"size",values:[]},{key:"src",values:[]},{key:"step",values:[]},{key:"type",values:["hidden","text","search","tel","url","email","password","datetime","date","month","week","time","datetime-local","number","range","color","checkbox","radio","file","submit","image","reset","button"]},{key:"value",values:[]},{key:"width",values:[]}]},{tag:"ins",attr:[{key:"cite",values:[]},{key:"datetime",values:[]}]},{tag:"kbd",attr:[]},{tag:"keygen",attr:[{key:"autofocus",values:["","autofocus"]},{key:"challenge",values:[]},{key:"disabled",values:["","disabled"]},{key:"form",values:[]},{key:"keytype",values:["RSA"]},{key:"name",values:[]}]},{tag:"label",attr:[{key:"for",values:[]},{key:"form",values:[]}]},{tag:"legend",attr:[]},{tag:"li",attr:[{key:"value",values:[]}]},{tag:"link",attr:[{key:"href",values:[]},{key:"hreflang",values:["en","es"]},{key:"media",values:["all","screen","print","embossed","braille","handheld","print","projection","screen","tty","tv","speech","3d-glasses","resolution [>][<][=] [X]dpi","resolution [>][<][=] [X]dpcm","device-aspect-ratio: 16/9","device-aspect-ratio: 4/3","device-aspect-ratio: 32/18","device-aspect-ratio: 1280/720","device-aspect-ratio: 2560/1440","orientation:portrait","orientation:landscape","device-height: [X]px","device-width: [X]px","-webkit-min-device-pixel-ratio: 2"]},{key:"type",values:[]},{key:"sizes",values:["all","16x16","16x16 32x32","16x16 32x32 64x64"]}]},{tag:"map",attr:[{key:"name",values:[]}]},{tag:"mark",attr:[]},{tag:"menu",attr:[{key:"type",values:["list","context","toolbar"]},{key:"label",values:[]}]},{tag:"meta",attr:[{key:"charset",attr:["utf-8"]},{key:"name",attr:["viewport","application-name","author","description","generator","keywords"]},{key:"content",attr:["","width=device-width","initial-scale=1, maximum-scale=1, minimun-scale=1, user-scale=no"]},{key:"http-equiv",attr:["content-language","content-type","default-style","refresh"]}]},{tag:"meter",attr:[{key:"value",values:[]},{key:"min",values:[]},{key:"low",values:[]},{key:"high",values:[]},{key:"max",values:[]},{key:"optimum",values:[]}]},{tag:"nav",attr:[]},{tag:"noframes",attr:[]},{tag:"noscript",attr:[]},{tag:"object",attr:[{key:"data",values:[]},{key:"type",values:[]},{key:"typemustmatch",values:["","typemustmatch"]},{key:"name",values:[]},{key:"usemap",values:[]},{key:"form",values:[]},{key:"width",values:[]},{key:"height",values:[]}]},{tag:"ol",attr:[{key:"reversed",values:["","reversed"]},{key:"start",values:[]},{key:"type",values:["1","a","A","i","I"]}]},{tag:"optgroup",attr:[{key:"disabled",values:["","disabled"]},{key:"label",values:[]}]},{tag:"option",attr:[{key:"disabled",values:["","disabled"]},{key:"label",values:[]},{key:"selected",values:["","selected"]},{key:"value",values:[]}]},{tag:"output",attr:[{key:"for",values:[]},{key:"form",values:[]},{key:"name",values:[]}]},{tag:"p",attr:[]},{tag:"param",attr:[{key:"name",values:[]},{key:"value",values:[]}]},{tag:"pre",attr:[]},{tag:"progress",attr:[{key:"value",values:[]},{key:"max",values:[]}]},{tag:"q",attr:[{key:"cite",values:[]}]},{tag:"rp",attr:[]},{tag:"rt",attr:[]},{tag:"ruby",attr:[]},{tag:"s",attr:[]},{tag:"samp",attr:[]},{tag:"script",attr:[{key:"type",values:["text/javascript"]},{key:"src",values:[]},{key:"async",values:["","async"]},{key:"defer",values:["","defer"]},{key:"charset",values:["utf-8"]}]},{tag:"section",attr:[]},{tag:"select",attr:[{key:"autofocus",values:["","autofocus"]},{key:"disabled",values:["","disabled"]},{key:"form",values:[]},{key:"multiple",values:["","multiple"]},{key:"name",values:[]},{key:"size",values:[]}]},{tag:"small",attr:[]},{tag:"source",attr:[{key:"src",values:[]},{key:"type",values:[]},{key:"media",values:[]}]},{tag:"span",attr:[]},{tag:"strike",attr:[]},{tag:"strong",attr:[]},{tag:"style",attr:[{key:"type",values:["text/css"]},{key:"media",values:["all","braille","print","projection","screen","speech"]},{key:"scoped",values:[]}]},{tag:"sub",attr:[]},{tag:"summary",attr:[]},{tag:"sup",attr:[]},{tag:"table",attr:[{key:"border",values:[]}]},{tag:"tbody",attr:[]},{tag:"td",attr:[{key:"colspan",values:[]},{key:"rowspan",values:[]},{key:"headers",values:[]}]},{tag:"textarea",attr:[{key:"autofocus",values:["","autofocus"]},{key:"disabled",values:["","disabled"]},{key:"dirname",values:[]},{key:"form",values:[]},{key:"maxlength",values:[]},{key:"name",values:[]},{key:"placeholder",values:[]},{key:"readonly",values:["","readonly"]},{key:"required",values:["","required"]},{key:"rows",values:[]},{key:"cols",values:[]},{key:"wrap",values:["soft","hard"]}]},{tag:"tfoot",attr:[]},{tag:"th",attr:[{key:"colspan",values:[]},{key:"rowspan",values:[]},{key:"headers",values:[]},{key:"scope",values:["row","col","rowgroup","colgroup"]}]},{tag:"thead",attr:[]},{tag:"time",attr:[{key:"datetime",values:[]}]},{tag:"title",attr:[]},{tag:"tr",attr:[]},{tag:"track",attr:[{key:"kind",values:["subtitles","captions","descriptions","chapters","metadata"]},{key:"src",values:[]},{key:"srclang",values:["en","es"]},{key:"label",values:[]},{key:"default",values:[]}]},{tag:"tt",attr:[]},{tag:"u",attr:[]},{tag:"ul",attr:[]},{tag:"var",attr:[]},{tag:"video",attr:[{key:"src",values:[]},{key:"crossorigin",values:["anonymous","use-credentials"]},{key:"poster",values:[]},{key:"preload",values:["auto","metadata","none"]},{key:"autoplay",values:["","autoplay"]},{key:"mediagroup",values:["movie"]},{key:"loop",values:["","loop"]},{key:"muted",values:["","muted"]},{key:"controls",values:["","controls"]},{key:"width",values:[]},{key:"height",values:[]}]},{tag:"wbr",attr:[]}],d=[{key:"accesskey",values:["a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","0","1","2","3","4","5","6","7","8","9"]},{key:"class",values:[]},{key:"contenteditable",values:["true","false"]},{key:"contextmenu",values:[]},{key:"dir",values:["ltr","rtl","auto"]},{key:"draggable",values:["true","false","auto"]},{key:"dropzone",values:["copy","move","link","string:","file:"]},{key:"hidden",values:["hidden"]},{key:"id",values:[]},{key:"inert",values:["inert"]},{key:"itemid",values:[]},{key:"itemprop",values:[]},{key:"itemref",values:[]},{key:"itemscope",values:["itemscope"]},{key:"itemtype",values:[]},{key:"lang",values:["en","es"]},{key:"spellcheck",values:["true","false"]},{key:"style",values:[]},{key:"tabindex",values:["1","2","3","4","5","6","7","8","9"]},{key:"title",values:[]},{key:"translate",values:["yes","no"]},{key:"onclick",values:[]},{key:"rel",values:["stylesheet","alternate","author","bookmark","help","license","next","nofollow","noreferrer","prefetch","prev","search","tag"]}];CodeMirror.htmlHint=function(b){return void 0==String.prototype.trim&&(String.prototype.trim=function(){return this.replace(/^\s+|\s+$/g,"")}),a(b,c,function(a,b){return a.getTokenAt(b)})}}();