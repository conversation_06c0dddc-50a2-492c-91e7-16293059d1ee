(function ($) {
  $.extend($.summernote.lang, {
    'id-ID': {
      font: {
        bold: '<PERSON><PERSON>',
        italic: 'Miring',
        underline: 'Garis bawah',
        clear: 'Bersihkan gaya',
        height: 'Jarak baris',
        strikethrough: 'Coret',
        size: 'Ukuran font'
      },
      image: {
        image: 'Gambar',
        insert: 'Sisipkan gambar',
        resizeFull: 'Ukuran penuh',
        resizeHalf: 'Ukuran 50%',
        resizeQuarter: 'Ukuran 25%',
        floatLeft: 'Rata kiri',
        floatRight: 'Rata kanan',
        floatNone: 'Tidak ada perataan',
        dragImageHere: 'Tarik gambar pada area ini',
        selectFromFiles: '<PERSON><PERSON>h gambar dari berkas',
        url: 'URL gambar',
        remove: 'Hapus Gambar'
      },
      video: {
        video: 'Video',
        videoLink: 'Link video',
        insert: 'Sisipkan video',
        url: 'Tautan video',
        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion, atau Youku)'
      },
      link: {
        link: 'Tautan',
        insert: '<PERSON><PERSON> tautan',
        unlink: 'Ha<PERSON> tautan',
        edit: 'Edit',
        textToDisplay: 'Tampilan teks',
        url: 'Tautan tujuan',
        openInNewWindow: 'Buka di jendela baru'
      },
      table: {
        table: 'Tabel'
      },
      hr: {
        insert: 'Masukkan garis horizontal'
      },
      style: {
        style: 'Gaya',
        normal: 'Normal',
        blockquote: 'Kutipan',
        pre: 'Kode',
        h1: 'Heading 1',
        h2: 'Heading 2',
        h3: 'Heading 3',
        h4: 'Heading 4',
        h5: 'Heading 5',
        h6: 'Heading 6'
      },
      lists: {
        unordered: 'Pencacahan',
        ordered: 'Penomoran'
      },
      options: {
        help: 'Bantuan',
        fullscreen: 'Layar penuh',
        codeview: 'Kode HTML'
      },
      paragraph: {
        paragraph: 'Paragraf',
        outdent: 'Outdent',
        indent: 'Indent',
        left: 'Rata kiri',
        center: 'Rata tengah',
        right: 'Rata kanan',
        justify: 'Rata kanan kiri'
      },
      color: {
        recent: 'Warna sekarang',
        more: 'Selengkapnya',
        background: 'Warna latar',
        foreground: 'Warna font',
        transparent: 'Transparan',
        setTransparent: 'Atur transparansi',
        reset: 'Atur ulang',
        resetToDefault: 'Kembalikan kesemula'
      },
      shortcut: {
        shortcuts: 'Jalan pintas',
        close: 'Keluar',
        textFormatting: 'Format teks',
        action: 'Aksi',
        paragraphFormatting: 'Format paragraf',
        documentStyle: 'Gaya dokumen'
      },
      history: {
        undo: 'Kembali',
        redo: 'Ulang'
      }
    }
  });
})(jQuery);
