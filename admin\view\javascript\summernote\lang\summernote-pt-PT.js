(function ($) {
  $.extend($.summernote.lang, {
    'pt-PT': {
      font: {
        bold: 'Negrito',
        italic: 'Itálico',
        underline: 'Sublinhado',
        clear: 'Remover estilo da fonte',
        height: '<PERSON>ura da linha',
        name: '<PERSON><PERSON>',
        strikethrough: 'Risca<PERSON>',
        size: '<PERSON><PERSON><PERSON> da fonte'
      },
      image: {
        image: 'Imagem',
        insert: 'Inserir imagem',
        resizeFull: 'Redimensionar Completo',
        resizeHalf: 'Redimensionar Metade',
        resizeQuarter: 'Redimensionar Um Quarto',
        floatLeft: 'Float Esquerda',
        floatRight: 'Float Direita',
        floatNone: 'Sem Float',
        dragImageHere: 'Arraste uma imagem para aqui',
        selectFromFiles: 'Selecione a partir dos arquivos',
        url: 'Endereço da imagem'
      },
      link: {
        link: 'Link',
        insert: 'Inserir ligação',
        unlink: 'Remover ligação',
        edit: 'Editar',
        textToDisplay: 'Texto para exibir',
        url: 'Que endereço esta licação leva?',
        openInNewWindow: 'Abrir numa nova janela'
      },
      table: {
        table: 'Tabela'
      },
      hr: {
        insert: 'Inserir linha horizontal'
      },
      style: {
        style: 'Estilo',
        normal: 'Normal',
        blockquote: 'Citação',
        pre: 'Código',
        h1: 'Título 1',
        h2: 'Título 2',
        h3: 'Título 3',
        h4: 'Título 4',
        h5: 'Título 5',
        h6: 'Título 6'
      },
      lists: {
        unordered: 'Lista com marcadores',
        ordered: 'Lista numerada'
      },
      options: {
        help: 'Ajuda',
        fullscreen: 'Janela Completa',
        codeview: 'Ver código-fonte'
      },
      paragraph: {
        paragraph: 'Parágrafo',
        outdent: 'Menor tabulação',
        indent: 'Maior tabulação',
        left: 'Alinhar à esquerda',
        center: 'Alinhar ao centro',
        right: 'Alinha à direita',
        justify: 'Justificado'
      },
      color: {
        recent: 'Cor recente',
        more: 'Mais cores',
        background: 'Fundo',
        foreground: 'Fonte',
        transparent: 'Transparente',
        setTransparent: 'Fundo transparente',
        reset: 'Restaurar',
        resetToDefault: 'Restaurar padrão'
      },
      shortcut: {
        shortcuts: 'Atalhos do teclado',
        close: 'Fechar',
        textFormatting: 'Formatação de texto',
        action: 'Ação',
        paragraphFormatting: 'Formatação de parágrafo',
        documentStyle: 'Estilo de documento'
      },
      history: {
        undo: 'Desfazer',
        redo: 'Refazer'
      }
    }
  });
})(jQuery);
