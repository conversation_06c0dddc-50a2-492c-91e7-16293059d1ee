<?php echo $header; ?><?php echo $column_left; ?>
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-messages" data-toggle="tooltip" title="<?php echo $button_save; ?>" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="<?php echo $cancel; ?>" data-toggle="tooltip" title="<?php echo $button_cancel; ?>" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1><?php echo $heading_title; ?></h1>
      <ul class="breadcrumb">
        <?php foreach ($breadcrumbs as $breadcrumb) { ?>
        <li><a href="<?php echo $breadcrumb['href']; ?>"><?php echo $breadcrumb['text']; ?></a></li>
        <?php } ?>
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    <?php if ($error_warning) { ?>
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> <?php echo $error_warning; ?>
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    <?php } ?>
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> <?php echo $text_form; ?></h3>
      </div>
      <div class="panel-body">
        <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-messages" class="form-horizontal">
          
          <!-- Наименование -->
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-name">
              <span data-toggle="tooltip" title="<?php echo $entry_name; ?>"><?php echo $entry_name; ?></span>
            </label>
            <div class="col-sm-10">
              <input type="text" name="name" value="<?php echo $name; ?>" placeholder="<?php echo $entry_name; ?>" id="input-name" class="form-control" />
              <?php if ($error_name) { ?>
              <div class="text-danger"><?php echo $error_name; ?></div>
              <?php } ?>
            </div>
          </div>

          <!-- Текст на съобщението -->
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-text">
              <span data-toggle="tooltip" title="<?php echo $entry_text; ?>"><?php echo $entry_text; ?></span>
            </label>
            <div class="col-sm-10">
              <textarea name="text" placeholder="<?php echo $entry_text; ?>" id="input-text" class="form-control summernote"><?php echo $text; ?></textarea>
              <?php if ($error_text) { ?>
              <div class="text-danger"><?php echo $error_text; ?></div>
              <?php } ?>
            </div>
          </div>

          <!-- Начална дата -->
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-start-date">
              <span data-toggle="tooltip" title="<?php echo $entry_start_date; ?>"><?php echo $entry_start_date; ?></span>
            </label>
            <div class="col-sm-10">
              <div class="input-group datetime">
                <input type="text" name="start_date" value="<?php echo $start_date; ?>" placeholder="<?php echo $entry_start_date; ?>" id="input-start-date" class="form-control" />
                <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
              <?php if ($error_start_date) { ?>
              <div class="text-danger"><?php echo $error_start_date; ?></div>
              <?php } ?>
            </div>
          </div>

          <!-- Крайна дата -->
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-end-date">
              <span data-toggle="tooltip" title="<?php echo $help_end_date; ?>"><?php echo $entry_end_date; ?></span>
            </label>
            <div class="col-sm-10">
              <div class="input-group datetime">
                <input type="text" name="end_date" value="<?php echo $end_date; ?>" placeholder="<?php echo $entry_end_date; ?>" id="input-end-date" class="form-control" />
                <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
              <div class="help-block"><?php echo $help_end_date; ?></div>
              <?php if ($error_end_date) { ?>
              <div class="text-danger"><?php echo $error_end_date; ?></div>
              <?php } ?>
            </div>
          </div>

          <!-- Място за показване -->
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-display-place">
              <span data-toggle="tooltip" title="<?php echo $help_display_place; ?>"><?php echo $entry_display_place; ?></span>
            </label>
            <div class="col-sm-10">
              <select name="display_place" id="input-display-place" class="form-control">
                <option value=""><?php echo $entry_display_place; ?></option>
                <?php foreach ($display_places as $key => $place_name) { ?>
                <?php if ($display_place == $key) { ?>
                <option value="<?php echo $key; ?>" selected="selected"><?php echo $place_name; ?></option>
                <?php } else { ?>
                <option value="<?php echo $key; ?>"><?php echo $place_name; ?></option>
                <?php } ?>
                <?php } ?>
              </select>
              <div class="help-block"><?php echo $help_display_place; ?></div>
              <?php if ($error_display_place) { ?>
              <div class="text-danger"><?php echo $error_display_place; ?></div>
              <?php } ?>
            </div>
          </div>

          <!-- Статус -->
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status"><?php echo $entry_status; ?></label>
            <div class="col-sm-10">
              <div class="toggle-switch">
                <input type="hidden" name="status" value="0" />
                <input type="checkbox" name="status" value="1" id="input-status" <?php echo $status ? 'checked="checked"' : ''; ?> />
                <label for="input-status" class="toggle-label">
                  <span class="toggle-inner"></span>
                  <span class="toggle-switch-slider"></span>
                </label>
                <span class="status-text"><?php echo $status ? $text_enabled : $text_disabled; ?></span>
              </div>
            </div>
          </div>

        </form>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // Инициализация на Summernote за rich text editor
    $('.summernote').summernote({
        height: 200,
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'italic', 'underline', 'clear']],
            ['fontname', ['fontname']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['height', ['height']],
            ['table', ['table']],
            ['insert', ['link', 'picture', 'hr']],
            ['view', ['fullscreen', 'codeview']],
            ['help', ['help']]
        ]
    });

    // Инициализация на datetime picker
    $('.datetime').datetimepicker({
        format: 'YYYY-MM-DD HH:mm',
        locale: 'bg',
        showTodayButton: true,
        showClear: true,
        showClose: true,
        sideBySide: true
    });

    // Toggle switch функционалност
    $('#input-status').change(function() {
        var statusText = $(this).is(':checked') ? '<?php echo $text_enabled; ?>' : '<?php echo $text_disabled; ?>';
        $('.status-text').text(statusText);
    });

    // Валидация на формата
    $('#form-messages').submit(function(e) {
        var isValid = true;
        var errorMessage = '';

        // Проверка на наименованието
        if ($('#input-name').val().trim().length < 3) {
            isValid = false;
            errorMessage += 'Наименованието трябва да бъде поне 3 символа.\n';
        }

        // Проверка на текста
        if ($('#input-text').summernote('isEmpty')) {
            isValid = false;
            errorMessage += 'Текстът на съобщението е задължителен.\n';
        }

        // Проверка на началната дата
        if ($('#input-start-date').val().trim() === '') {
            isValid = false;
            errorMessage += 'Началната дата е задължителна.\n';
        }

        // Проверка на мястото за показване
        if ($('#input-display-place').val() === '') {
            isValid = false;
            errorMessage += 'Мястото за показване е задължително.\n';
        }

        // Проверка дали крайната дата е след началната
        var startDate = $('#input-start-date').val();
        var endDate = $('#input-end-date').val();
        
        if (startDate && endDate && new Date(endDate) <= new Date(startDate)) {
            isValid = false;
            errorMessage += 'Крайната дата трябва да бъде след началната дата.\n';
        }

        if (!isValid) {
            alert(errorMessage);
            e.preventDefault();
            return false;
        }

        return true;
    });
});
</script>

<style>
.toggle-switch {
    position: relative;
    display: inline-block;
    vertical-align: middle;
}

.toggle-switch input[type="checkbox"] {
    display: none;
}

.toggle-label {
    display: block;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid #999999;
    border-radius: 20px;
    margin: 0;
    width: 60px;
    height: 30px;
    position: relative;
}

.toggle-inner {
    display: block;
    width: 200%;
    margin-left: -100%;
    transition: margin 0.3s ease-in 0s;
}

.toggle-inner:before,
.toggle-inner:after {
    display: block;
    float: left;
    width: 50%;
    height: 26px;
    padding: 0;
    line-height: 26px;
    font-size: 12px;
    color: white;
    font-weight: bold;
    box-sizing: border-box;
}

.toggle-inner:before {
    content: "ВКЛ";
    text-align: center;
    padding-left: 10px;
    background-color: #28a745;
    color: #FFFFFF;
}

.toggle-inner:after {
    content: "ИЗКЛ";
    text-align: center;
    padding-right: 10px;
    background-color: #dc3545;
    color: #FFFFFF;
    text-align: right;
}

.toggle-switch-slider {
    display: block;
    width: 22px;
    height: 22px;
    margin: 2px;
    background: #FFFFFF;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 32px;
    border: 2px solid #999999;
    border-radius: 20px;
    transition: all 0.3s ease-in 0s;
}

.toggle-switch input[type="checkbox"]:checked + .toggle-label .toggle-inner {
    margin-left: 0;
}

.toggle-switch input[type="checkbox"]:checked + .toggle-label .toggle-switch-slider {
    right: 0px;
}

.status-text {
    margin-left: 10px;
    font-weight: bold;
    vertical-align: middle;
}

.form-group.required .control-label:before {
    content: "* ";
    color: #F00;
}

.panel-heading {
    background: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.btn-primary {
    background: linear-gradient(to bottom, #007bff 0%, #0056b3 100%);
    border-color: #0056b3;
}

.alert-dismissible {
    padding-right: 35px;
}

.help-block {
    color: #737373;
    font-size: 12px;
    margin-top: 5px;
}

.summernote {
    border: 1px solid #ddd;
}
</style>

<?php echo $footer; ?>
