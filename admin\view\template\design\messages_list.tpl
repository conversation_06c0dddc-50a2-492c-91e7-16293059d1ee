<?php echo $header; ?><?php echo $column_left; ?>
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="<?php echo $add; ?>" data-toggle="tooltip" title="<?php echo $button_add; ?>" class="btn btn-primary"><i class="fa fa-plus"></i></a>
        <button type="button" data-toggle="tooltip" title="<?php echo $button_delete; ?>" class="btn btn-danger" onclick="confirm('<?php echo $text_confirm; ?>') ? $('#form-messages').submit() : false;"><i class="fa fa-trash-o"></i></button>
        <div class="btn-group">
          <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="fa fa-cog"></i> Bulk операции <span class="caret"></span>
          </button>
          <ul class="dropdown-menu">
            <li><a href="#" onclick="bulkStatus(1)"><i class="fa fa-check-circle text-success"></i> <?php echo $button_enable; ?></a></li>
            <li><a href="#" onclick="bulkStatus(0)"><i class="fa fa-times-circle text-danger"></i> <?php echo $button_disable; ?></a></li>
          </ul>
        </div>
      </div>
      <h1><?php echo $heading_title; ?></h1>
      <ul class="breadcrumb">
        <?php foreach ($breadcrumbs as $breadcrumb) { ?>
        <li><a href="<?php echo $breadcrumb['href']; ?>"><?php echo $breadcrumb['text']; ?></a></li>
        <?php } ?>
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    <?php if ($error_warning) { ?>
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> <?php echo $error_warning; ?>
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    <?php } ?>
    <?php if ($success) { ?>
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> <?php echo $success; ?>
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    <?php } ?>
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> <?php echo $text_list; ?></h3>
      </div>
      <div class="panel-body">
        <form action="<?php echo $delete; ?>" method="post" enctype="multipart/form-data" id="form-messages">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                  <td class="text-left"><?php if ($sort == 'name') { ?>
                    <a href="<?php echo $sort_name; ?>" class="<?php echo strtolower($order); ?>"><?php echo $column_name; ?></a>
                    <?php } else { ?>
                    <a href="<?php echo $sort_name; ?>"><?php echo $column_name; ?></a>
                    <?php } ?></td>
                  <td class="text-left"><?php if ($sort == 'display_place') { ?>
                    <a href="<?php echo $sort_display_place; ?>" class="<?php echo strtolower($order); ?>"><?php echo $column_display_place; ?></a>
                    <?php } else { ?>
                    <a href="<?php echo $sort_display_place; ?>"><?php echo $column_display_place; ?></a>
                    <?php } ?></td>
                  <td class="text-left"><?php if ($sort == 'start_date') { ?>
                    <a href="<?php echo $sort_start_date; ?>" class="<?php echo strtolower($order); ?>"><?php echo $column_start_date; ?></a>
                    <?php } else { ?>
                    <a href="<?php echo $sort_start_date; ?>"><?php echo $column_start_date; ?></a>
                    <?php } ?></td>
                  <td class="text-left"><?php echo $column_end_date; ?></td>
                  <td class="text-left"><?php if ($sort == 'status') { ?>
                    <a href="<?php echo $sort_status; ?>" class="<?php echo strtolower($order); ?>"><?php echo $column_status; ?></a>
                    <?php } else { ?>
                    <a href="<?php echo $sort_status; ?>"><?php echo $column_status; ?></a>
                    <?php } ?></td>
                  <td class="text-right"><?php echo $column_action; ?></td>
                </tr>
              </thead>
              <tbody>
                <?php if ($messages) { ?>
                <?php foreach ($messages as $message) { ?>
                <tr>
                  <td class="text-center"><?php if (in_array($message['message_id'], $selected)) { ?>
                    <input type="checkbox" name="selected[]" value="<?php echo $message['message_id']; ?>" checked="checked" />
                    <?php } else { ?>
                    <input type="checkbox" name="selected[]" value="<?php echo $message['message_id']; ?>" />
                    <?php } ?></td>
                  <td class="text-left">
                    <strong><?php echo $message['name']; ?></strong>
                  </td>
                  <td class="text-left">
                    <span class="label label-info"><?php echo $message['display_place']; ?></span>
                  </td>
                  <td class="text-left">
                    <i class="fa fa-calendar"></i> <?php echo $message['start_date']; ?>
                  </td>
                  <td class="text-left">
                    <?php if ($message['end_date'] != $text_no_end_date) { ?>
                    <i class="fa fa-calendar"></i> <?php echo $message['end_date']; ?>
                    <?php } else { ?>
                    <span class="text-muted"><i class="fa fa-infinity"></i> <?php echo $message['end_date']; ?></span>
                    <?php } ?>
                  </td>
                  <td class="text-left">
                    <?php if ($message['status'] == $text_enabled) { ?>
                    <span class="label label-success"><?php echo $message['status']; ?></span>
                    <?php } else { ?>
                    <span class="label label-danger"><?php echo $message['status']; ?></span>
                    <?php } ?>
                  </td>
                  <td class="text-right">
                    <a href="<?php echo $message['edit']; ?>" data-toggle="tooltip" title="<?php echo $button_edit; ?>" class="btn btn-primary btn-sm"><i class="fa fa-pencil"></i></a>
                  </td>
                </tr>
                <?php } ?>
                <?php } else { ?>
                <tr>
                  <td class="text-center" colspan="7"><?php echo $text_no_results; ?></td>
                </tr>
                <?php } ?>
              </tbody>
            </table>
          </div>
        </form>
        <div class="row">
          <div class="col-sm-6 text-left"><?php echo $pagination; ?></div>
          <div class="col-sm-6 text-right"><?php echo $results; ?></div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
function bulkStatus(status) {
    var selected = $('input[name*=\'selected\']:checked');
    
    if (selected.length == 0) {
        alert('Моля, изберете поне едно съобщение!');
        return false;
    }
    
    var statusText = status == 1 ? 'включите' : 'изключите';
    
    if (confirm('Сигурни ли сте, че искате да ' + statusText + ' избраните съобщения?')) {
        var selectedIds = [];
        selected.each(function() {
            selectedIds.push($(this).val());
        });
        
        $.ajax({
            url: '<?php echo $bulk_status; ?>',
            type: 'post',
            data: {
                'selected': selectedIds,
                'status': status
            },
            dataType: 'json',
            success: function(json) {
                if (json['success']) {
                    location.reload();
                } else if (json['error']) {
                    alert(json['error']);
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert('Възникна грешка при обработката на заявката!');
            }
        });
    }
}
</script>

<style>
.table > thead > tr > th,
.table > tbody > tr > td {
    vertical-align: middle;
}

.label {
    font-size: 11px;
    padding: 4px 8px;
}

.btn-group .dropdown-menu {
    min-width: 160px;
}

.dropdown-menu > li > a {
    padding: 8px 15px;
}

.dropdown-menu > li > a:hover {
    background-color: #f5f5f5;
}

.alert-dismissible {
    padding-right: 35px;
}

.panel-heading {
    background: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.btn-primary {
    background: linear-gradient(to bottom, #007bff 0%, #0056b3 100%);
    border-color: #0056b3;
}

.btn-success {
    background: linear-gradient(to bottom, #28a745 0%, #1e7e34 100%);
    border-color: #1e7e34;
}

.btn-danger {
    background: linear-gradient(to bottom, #dc3545 0%, #bd2130 100%);
    border-color: #bd2130;
}
</style>

<?php echo $footer; ?>
