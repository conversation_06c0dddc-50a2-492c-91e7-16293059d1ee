<table class="table table-bordered">
  <?php if ($country_match) { ?>
  <tr>
    <td><?php echo $text_country_match; ?></td>
    <td><?php echo $country_match; ?></td>
  </tr>
  <?php } ?>
  <?php if ($country_code) { ?>
  <tr>
    <td><?php echo $text_country_code; ?></td>
    <td><?php echo $country_code; ?></td>
  </tr>
  <?php } ?>
  <?php if ($high_risk_country) { ?>
  <tr>
    <td><?php echo $text_high_risk_country; ?></td>
    <td><?php echo $high_risk_country; ?></td>
  </tr>
  <?php } ?>
  <?php if ($distance) { ?>
  <tr>
    <td><?php echo $text_distance; ?></td>
    <td><?php echo $distance; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_region) { ?>
  <tr>
    <td><?php echo $text_ip_region; ?></td>
    <td><?php echo $ip_region; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_city) { ?>
  <tr>
    <td><?php echo $text_ip_city; ?></td>
    <td><?php echo $ip_city; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_latitude) { ?>
  <tr>
    <td><?php echo $text_ip_latitude; ?></td>
    <td><?php echo $ip_latitude; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_longitude) { ?>
  <tr>
    <td><?php echo $text_ip_longitude; ?></td>
    <td><?php echo $ip_longitude; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_isp) { ?>
  <tr>
    <td><?php echo $text_ip_isp; ?></td>
    <td><?php echo $ip_isp; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_org) { ?>
  <tr>
    <td><?php echo $text_ip_org; ?></td>
    <td><?php echo $ip_org; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_asnum) { ?>
  <tr>
    <td><?php echo $text_ip_asnum; ?></td>
    <td><?php echo $ip_asnum; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_user_type) { ?>
  <tr>
    <td><?php echo $text_ip_user_type; ?></td>
    <td><?php echo $ip_user_type; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_country_confidence) { ?>
  <tr>
    <td><?php echo $text_ip_country_confidence; ?></td>
    <td><?php echo $ip_country_confidence; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_region_confidence) { ?>
  <tr>
    <td><?php echo $text_ip_region_confidence; ?></td>
    <td><?php echo $ip_region_confidence; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_city_confidence) { ?>
  <tr>
    <td><?php echo $text_ip_city_confidence; ?></td>
    <td><?php echo $ip_city_confidence; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_postal_confidence) { ?>
  <tr>
    <td><?php echo $text_ip_postal_confidence; ?></td>
    <td><?php echo $ip_postal_confidence; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_postal_code) { ?>
  <tr>
    <td><?php echo $text_ip_postal_code; ?></td>
    <td><?php echo $ip_postal_code; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_accuracy_radius) { ?>
  <tr>
    <td><?php echo $text_ip_accuracy_radius; ?></td>
    <td><?php echo $ip_accuracy_radius; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_net_speed_cell) { ?>
  <tr>
    <td><?php echo $text_ip_net_speed_cell; ?></td>
    <td><?php echo $ip_net_speed_cell; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_metro_code) { ?>
  <tr>
    <td><?php echo $text_ip_metro_code; ?></td>
    <td><?php echo $ip_metro_code; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_area_code) { ?>
  <tr>
    <td><?php echo $text_ip_area_code; ?></td>
    <td><?php echo $ip_area_code; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_time_zone) { ?>
  <tr>
    <td><?php echo $text_ip_time_zone; ?></td>
    <td><?php echo $ip_time_zone; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_region_name) { ?>
  <tr>
    <td><?php echo $text_ip_region_name; ?></td>
    <td><?php echo $ip_region_name; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_domain) { ?>
  <tr>
    <td><?php echo $text_ip_domain; ?></td>
    <td><?php echo $ip_domain; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_country_name) { ?>
  <tr>
    <td><?php echo $text_ip_country_name; ?></td>
    <td><?php echo $ip_country_name; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_continent_code) { ?>
  <tr>
    <td><?php echo $text_ip_continent_code; ?></td>
    <td><?php echo $ip_continent_code; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ip_corporate_proxy) { ?>
  <tr>
    <td><?php echo $text_ip_corporate_proxy; ?></td>
    <td><?php echo $ip_corporate_proxy; ?></td>
  </tr>
  <?php } ?>
  <?php if ($anonymous_proxy) { ?>
  <tr>
    <td><?php echo $text_anonymous_proxy; ?></td>
    <td><?php echo $anonymous_proxy; ?></td>
  </tr>
  <?php } ?>
  <?php if ($proxy_score) { ?>
  <tr>
    <td><?php echo $text_proxy_score; ?></td>
    <td><?php echo $proxy_score; ?></td>
  </tr>
  <?php } ?>
  <?php if ($is_trans_proxy) { ?>
  <tr>
    <td><?php echo $text_is_trans_proxy; ?></td>
    <td><?php echo $is_trans_proxy; ?></td>
  </tr>
  <?php } ?>
  <?php if ($free_mail) { ?>
  <tr>
    <td><?php echo $text_free_mail; ?></td>
    <td><?php echo $free_mail; ?></td>
  </tr>
  <?php } ?>
  <?php if ($carder_email) { ?>
  <tr>
    <td><?php echo $text_carder_email; ?></td>
    <td><?php echo $carder_email; ?></td>
  </tr>
  <?php } ?>
  <?php if ($high_risk_username) { ?>
  <tr>
    <td><?php echo $text_high_risk_username; ?></td>
    <td><?php echo $high_risk_username; ?></td>
  </tr>
  <?php } ?>
  <?php if ($high_risk_password) { ?>
  <tr>
    <td><?php echo $text_high_risk_password; ?></td>
    <td><?php echo $high_risk_password; ?></td>
  </tr>
  <?php } ?>
  <?php if ($bin_match) { ?>
  <tr>
    <td><?php echo $text_bin_match; ?></td>
    <td><?php echo $bin_match; ?></td>
  </tr>
  <?php } ?>
  <?php if ($bin_country) { ?>
  <tr>
    <td><?php echo $text_bin_country; ?></td>
    <td><?php echo $bin_country; ?></td>
  </tr>
  <?php } ?>
  <?php if ($bin_name_match) { ?>
  <tr>
    <td><?php echo $text_bin_name_match; ?></td>
    <td><?php echo $bin_name_match; ?></td>
  </tr>
  <?php } ?>
  <?php if ($bin_name) { ?>
  <tr>
    <td><?php echo $text_bin_name; ?></td>
    <td><?php echo $bin_name; ?></td>
  </tr>
  <?php } ?>
  <?php if ($bin_phone_match) { ?>
  <tr>
    <td><?php echo $text_bin_phone_match; ?></td>
    <td><?php echo $bin_phone_match; ?></td>
  </tr>
  <?php } ?>
  <?php if ($bin_phone) { ?>
  <tr>
    <td><?php echo $text_bin_phone; ?></td>
    <td><?php echo $bin_phone; ?></td>
  </tr>
  <?php } ?>
  <?php if ($customer_phone_in_billing_location) { ?>
  <tr>
    <td><?php echo $text_customer_phone_in_billing_location; ?></td>
    <td><?php echo $customer_phone_in_billing_location; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ship_forward) { ?>
  <tr>
    <td><?php echo $text_ship_forward; ?></td>
    <td><?php echo $ship_forward; ?></td>
  </tr>
  <?php } ?>
  <?php if ($city_postal_match) { ?>
  <tr>
    <td><?php echo $text_city_postal_match; ?></td>
    <td><?php echo $city_postal_match; ?></td>
  </tr>
  <?php } ?>
  <?php if ($ship_city_postal_match) { ?>
  <tr>
    <td><?php echo $text_ship_city_postal_match; ?></td>
    <td><?php echo $ship_city_postal_match; ?></td>
  </tr>
  <?php } ?>
  <?php if ($score) { ?>
  <tr>
    <td><?php echo $text_score; ?></td>
    <td><?php echo $score; ?></td>
  </tr>
  <?php } ?>
  <?php if ($explanation) { ?>
  <tr>
    <td><?php echo $text_explanation; ?></td>
    <td><?php echo $explanation; ?></td>
  </tr>
  <?php } ?>
  <?php if ($risk_score) { ?>
  <tr>
    <td><?php echo $text_risk_score; ?></td>
    <td><?php echo $risk_score; ?></td>
  </tr>
  <?php } ?>
  <?php if ($queries_remaining) { ?>
  <tr>
    <td><?php echo $text_queries_remaining; ?></td>
    <td><?php echo $queries_remaining; ?></td>
  </tr>
  <?php } ?>
  <?php if ($maxmind_id) { ?>
  <tr>
    <td><?php echo $text_maxmind_id; ?></td>
    <td><?php echo $maxmind_id; ?></td>
  </tr>
  <?php } ?>
  <?php if ($error) { ?>
  <tr>
    <td><?php echo $text_error; ?></td>
    <td><?php echo $error; ?></td>
  </tr>
  <?php } ?>
</table>
