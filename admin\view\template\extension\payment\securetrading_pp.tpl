<?php echo $header; ?><?php echo $column_left; ?>
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-securetrading-pp" data-toggle="tooltip" title="<?php echo $button_save; ?>" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="<?php echo $cancel; ?>" data-toggle="tooltip" title="<?php echo $button_cancel; ?>" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1><?php echo $heading_title; ?></h1>
      <ul class="breadcrumb">
        <?php foreach ($breadcrumbs as $breadcrumb) { ?>
        <li><a href="<?php echo $breadcrumb['href']; ?>"><?php echo $breadcrumb['text']; ?></a></li>
        <?php } ?>
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    <?php if ($error_warning) { ?>
    <div class="alert alert-danger"><i class="fa fa-exclamation-circle"></i> <?php echo $error_warning; ?>
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    <?php } ?>
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> <?php echo $heading_title; ?></h3>
      </div>
      <div class="panel-body">
        <form action="<?php echo $action; ?>" method="post" enctype="multipart/form-data" id="form-securetrading-pp" class="form-horizontal">
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="securetrading_pp_site_reference"><?php echo $entry_site_reference; ?></label>
            <div class="col-sm-10">
              <input type="text" name="securetrading_pp_site_reference" value="<?php echo $securetrading_pp_site_reference; ?>" placeholder="<?php echo $entry_site_reference; ?>" id="securetrading_pp_site_reference" class="form-control" />
              <?php if ($error_site_reference) { ?>
              <div class="text-danger"><?php echo $error_site_reference; ?></div>
              <?php } ?>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_username"><?php echo $entry_username; ?></label>
            <div class="col-sm-10">
              <input type="text" name="securetrading_pp_username" value="<?php echo $securetrading_pp_username; ?>" placeholder="<?php echo $entry_username; ?>" id="securetrading_pp_username" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_password"><?php echo $entry_password; ?></label>
            <div class="col-sm-10">
              <input type="text" name="securetrading_pp_password" value="<?php echo $securetrading_pp_password; ?>" placeholder="<?php echo $entry_password; ?>" id="securetrading_pp_password" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_site_security_status"><?php echo $entry_site_security_status; ?></label>
            <div class="col-sm-10">
              <select name="securetrading_pp_site_security_status" id="securetrading_pp_status" class="form-control">
                <?php if ($securetrading_pp_site_security_status == 1) { ?>
                <option value="1" selected="selected"><?php echo $text_enabled; ?></option>
                <?php } else { ?>
                <option value="1"><?php echo $text_enabled; ?></option>
                <?php } ?>
                <?php if ($securetrading_pp_site_security_status == 0) { ?>
                <option value="0" selected="selected"><?php echo $text_disabled; ?></option>
                <?php } else { ?>
                <option value="0"><?php echo $text_disabled; ?></option>
                <?php } ?>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_site_security_password"><?php echo $entry_site_security_password; ?></label>
            <div class="col-sm-10">
              <input type="text" name="securetrading_pp_site_security_password" value="<?php echo $securetrading_pp_site_security_password; ?>" placeholder="<?php echo $entry_site_security_password; ?>" id="securetrading_pp_site_security_password" class="form-control" />
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="securetrading_pp_notification_password"><?php echo $entry_notification_password; ?></label>
            <div class="col-sm-10">
              <input type="text" name="securetrading_pp_notification_password" value="<?php echo $securetrading_pp_notification_password; ?>" placeholder="<?php echo $entry_notification_password; ?>" id="securetrading_pp_site_security_password" class="form-control" />
              <?php if ($error_notification_password) { ?>
              <div class="text-danger"><?php echo $error_notification_password; ?></div>
              <?php } ?>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_webservice_username"><?php echo $entry_username; ?></label>
            <div class="col-sm-10">
              <input type="text" name="securetrading_pp_webservice_username" value="<?php echo $securetrading_pp_webservice_username; ?>" placeholder="<?php echo $entry_username; ?>" id="securetrading_pp_webservice_username" class="form-control" />
              <span class="help-block"><?php echo $help_username; ?></span> </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_webservice_password"><?php echo $entry_password; ?></label>
            <div class="col-sm-10">
              <input type="text" name="securetrading_pp_webservice_password" value="<?php echo $securetrading_pp_webservice_password; ?>" placeholder="<?php echo $entry_password; ?>" id="securetrading_pp_webservice_username" class="form-control" />
              <span class="help-block"><?php echo $help_password; ?></span> </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="securetrading_pp_cards_accepted"><?php echo $entry_cards_accepted; ?></label>
            <div class="col-sm-10">
              <?php foreach ($cards as $key => $value) { ?>
              <div class="checkbox">
                <label>
                  <?php if (in_array($key, $securetrading_pp_cards_accepted)) { ?>
                  <input type="checkbox" checked="checked" name="securetrading_pp_cards_accepted[]" value="<?php echo $key; ?>" />
                  <?php } else { ?>
                  <input type="checkbox" name="securetrading_pp_cards_accepted[]" value="<?php echo $key; ?>" />
                  <?php } ?>
                  <?php echo $value; ?> </label>
              </div>
              <?php } ?>
              <?php if ($error_cards_accepted) { ?>
              <div class="text-danger"><?php echo $error_cards_accepted; ?></div>
              <?php } ?>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_settle_status"><?php echo $entry_settle_status; ?></label>
            <div class="col-sm-10">
              <select name="securetrading_pp_settle_status" id="securetrading_pp_settle_status" class="form-control">
                <?php foreach ($settlement_statuses as $key => $value) { ?>
                <?php if ($key == $securetrading_pp_settle_status) { ?>
                <option value="<?php echo $key; ?>" selected="selected"><?php echo $value; ?></option>
                <?php } else { ?>
                <option value="<?php echo $key; ?>"><?php echo $value; ?></option>
                <?php } ?>
                <?php } ?>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_settle_due_date"><?php echo $entry_settle_due_date; ?></label>
            <div class="col-sm-10">
              <select name="securetrading_pp_settle_due_date" id="securetrading_pp_settle_due_date" class="form-control">
                <?php if ($securetrading_pp_settle_due_date == 0) { ?>
                <option value="0" selected="selected"><?php echo $text_process_immediately; ?></option>
                <?php } else { ?>
                <option value="0"><?php echo $text_process_immediately; ?></option>
                <?php } ?>
                <?php for ($i = 1; $i < 8; $i++) { ?>
                <?php if ($i == $securetrading_pp_settle_due_date) { ?>
                <option value="<?php echo $i ?>" selected="selected"><?php echo sprintf($text_wait_x_days, $i); ?></option>
                <?php } else { ?>
                <option value="<?php echo $i ?>"><?php echo sprintf($text_wait_x_days, $i); ?></option>
                <?php } ?>
                <?php } ?>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_total"><?php echo $entry_total; ?></label>
            <div class="col-sm-10">
              <input type="text" name="securetrading_pp_total" value="<?php echo $securetrading_pp_total; ?>" placeholder="<?php echo $entry_total; ?>" id="securetrading_pp_total" class="form-control" />
              <span class="help-block"><?php echo $help_total; ?></span> </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_order_status_id"><?php echo $entry_order_status; ?></label>
            <div class="col-sm-10">
              <select name="securetrading_pp_order_status_id" id="securetrading_pp_order_status_id" class="form-control">
                <?php foreach ($order_statuses as $order_status) { ?>
                <?php if ($order_status['order_status_id'] == $securetrading_pp_order_status_id) { ?>
                <option value="<?php echo $order_status['order_status_id']; ?>" selected="selected"><?php echo $order_status['name']; ?></option>
                <?php } else { ?>
                <option value="<?php echo $order_status['order_status_id']; ?>"><?php echo $order_status['name']; ?></option>
                <?php } ?>
                <?php } ?>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_declined_order_status_id"><?php echo $entry_declined_order_status; ?></label>
            <div class="col-sm-10">
              <select name="securetrading_pp_declined_order_status_id" id="securetrading_pp_declined_order_status_id" class="form-control">
                <?php foreach ($order_statuses as $order_status) { ?>
                <?php if ($order_status['order_status_id'] == $securetrading_pp_declined_order_status_id) { ?>
                <option value="<?php echo $order_status['order_status_id']; ?>" selected="selected"><?php echo $order_status['name']; ?></option>
                <?php } else { ?>
                <option value="<?php echo $order_status['order_status_id']; ?>"><?php echo $order_status['name']; ?></option>
                <?php } ?>
                <?php } ?>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_refunded_order_status_id"><?php echo $entry_refunded_order_status; ?></label>
            <div class="col-sm-10">
              <select name="securetrading_pp_refunded_order_status_id" id="securetrading_pp_refunded_order_status_id" class="form-control">
                <?php foreach ($order_statuses as $order_status) { ?>
                <?php if ($order_status['order_status_id'] == $securetrading_pp_refunded_order_status_id) { ?>
                <option value="<?php echo $order_status['order_status_id']; ?>" selected="selected"><?php echo $order_status['name']; ?></option>
                <?php } else { ?>
                <option value="<?php echo $order_status['order_status_id']; ?>"><?php echo $order_status['name']; ?></option>
                <?php } ?>
                <?php } ?>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_authorisation_reversed_order_status_id"><?php echo $entry_authorisation_reversed_order_status; ?></label>
            <div class="col-sm-10">
              <select name="securetrading_pp_authorisation_reversed_order_status_id" id="securetrading_pp_authorisation_reversed_order_status_id" class="form-control">
                <?php foreach ($order_statuses as $order_status) { ?>
                <?php if ($order_status['order_status_id'] == $securetrading_pp_authorisation_reversed_order_status_id) { ?>
                <option value="<?php echo $order_status['order_status_id']; ?>" selected="selected"><?php echo $order_status['name']; ?></option>
                <?php } else { ?>
                <option value="<?php echo $order_status['order_status_id']; ?>"><?php echo $order_status['name']; ?></option>
                <?php } ?>
                <?php } ?>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_geo_zone_id"><?php echo $entry_geo_zone; ?></label>
            <div class="col-sm-10">
              <select name="securetrading_pp_geo_zone_id" id="securetrading_pp_geo_zone_id" class="form-control">
                <?php if ($securetrading_pp_geo_zone_id == 0) { ?>
                <option value="0" selected="selected"><?php echo $text_all_geo_zones; ?></option>
                <?php } else { ?>
                <option value="0"><?php echo $text_all_geo_zones; ?></option>
                <?php } ?>
                <?php foreach ($geo_zones as $geo_zone) { ?>
                <?php if ($securetrading_pp_geo_zone_id == $geo_zone['geo_zone_id']) { ?>
                <option value="<?php echo $geo_zone['geo_zone_id']; ?>" selected="selected"><?php echo $geo_zone['name']; ?></option>
                <?php } else { ?>
                <option value="<?php echo $geo_zone['geo_zone_id']; ?>"><?php echo $geo_zone['name']; ?></option>
                <?php } ?>
                <?php } ?>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_status"><?php echo $entry_status; ?></label>
            <div class="col-sm-10">
              <select name="securetrading_pp_status" id="securetrading_pp_status" class="form-control">
                <?php if ($securetrading_pp_status == 1) { ?>
                <option value="1" selected="selected"><?php echo $text_enabled; ?></option>
                <?php } else { ?>
                <option value="1"><?php echo $text_enabled; ?></option>
                <?php } ?>
                <?php if ($securetrading_pp_status == 0) { ?>
                <option value="0" selected="selected"><?php echo $text_disabled; ?></option>
                <?php } else { ?>
                <option value="0"><?php echo $text_disabled; ?></option>
                <?php } ?>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_sort_order"><?php echo $entry_sort_order; ?></label>
            <div class="col-sm-10">
              <input type="text" name="securetrading_pp_sort_order" value="<?php echo $securetrading_pp_sort_order; ?>" placeholder="<?php echo $entry_sort_order; ?>" id="securetrading_pp_sort_order" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_parent_css"><?php echo $entry_parent_css; ?></label>
            <div class="col-sm-10">
              <input type="text" name="securetrading_pp_parent_css" value="<?php echo $securetrading_pp_parent_css; ?>" placeholder="<?php echo $entry_parent_css; ?>" id="securetrading_pp_parent_css" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_pp_child_css"><?php echo $entry_child_css; ?></label>
            <div class="col-sm-10">
              <input type="text" name="securetrading_pp_child_css" value="<?php echo $securetrading_pp_child_css; ?>" placeholder="<?php echo $entry_child_css; ?>" id="securetrading_pp_child_css" class="form-control" />
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<?php echo $footer; ?>
