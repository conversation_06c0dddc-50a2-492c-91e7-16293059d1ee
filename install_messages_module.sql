-- SQL скрипт за създаване на модула за управление на съобщения
-- OpenCart 2.3 Messages Module Installation Script
-- Дата: <?php echo date('Y-m-d H:i:s'); ?>

-- Създаване на таблицата за съобщения
CREATE TABLE IF NOT EXISTS `{{prefix}}messages` (
  `message_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT 'Наименование на съобщението',
  `text` text NOT NULL COMMENT 'Текст на съобщението (HTML съдържание)',
  `start_date` datetime NOT NULL COMMENT 'Начална дата за показване',
  `end_date` datetime DEFAULT NULL COMMENT 'Крайна дата за показване (NULL = без крайна дата)',
  `display_place` varchar(100) NOT NULL COMMENT 'Място за показване (ключова дума)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Статус (0=изключено, 1=включено)',
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата на създаване',
  `date_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата на последна промяна',
  PRIMARY KEY (`message_id`),
  KEY `idx_status` (`status`),
  KEY `idx_display_place` (`display_place`),
  KEY `idx_dates` (`start_date`, `end_date`),
  KEY `idx_active` (`status`, `start_date`, `end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='Таблица за съобщения';

-- Вмъкване на примерни данни
INSERT INTO `{{prefix}}messages` (`name`, `text`, `start_date`, `end_date`, `display_place`, `status`) VALUES
('Добре дошли в нашия магазин!', '<p><strong>Добре дошли в нашия онлайн магазин!</strong></p><p>Тук ще намерите най-качествените продукти на най-добри цени. Разгледайте нашите категории и открийте невероятни оферти.</p>', NOW(), NULL, 'index_page', 1),
('Специална промоция - 20% отстъпка', '<div class="alert alert-success"><h4>🎉 Специална промоция!</h4><p>Получете <strong>20% отстъпка</strong> от всички продукти с код: <code>PROMO20</code></p><p><small>Валидна до края на месеца</small></p></div>', NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 'index_page', 1);

-- Добавяне на права за достъп в админ панела (ако е необходимо)
-- Тези заявки може да се изпълнят ръчно в зависимост от настройките на правата

-- INSERT INTO `{{prefix}}user_group` (`user_group_id`, `name`, `permission`) VALUES 
-- (1, 'Administrator', '{"access":["design\/messages"],"modify":["design\/messages"]}');

-- Показване на информация за успешна инсталация
SELECT 'Модулът за съобщения е успешно инсталиран!' AS 'Статус на инсталацията';

-- Показване на създадените записи
SELECT 
    message_id,
    name,
    display_place,
    start_date,
    end_date,
    status,
    date_created
FROM `{{prefix}}messages` 
ORDER BY date_created DESC;

-- Полезни заявки за управление на съобщенията:

-- 1. Показване на всички активни съобщения
-- SELECT * FROM `{{prefix}}messages` 
-- WHERE status = 1 
-- AND start_date <= NOW() 
-- AND (end_date IS NULL OR end_date >= NOW())
-- ORDER BY start_date ASC;

-- 2. Показване на съобщения за конкретно място
-- SELECT * FROM `{{prefix}}messages` 
-- WHERE status = 1 
-- AND display_place = 'index_page'
-- AND start_date <= NOW() 
-- AND (end_date IS NULL OR end_date >= NOW())
-- ORDER BY start_date ASC;

-- 3. Показване на изтекли съобщения
-- SELECT * FROM `{{prefix}}messages` 
-- WHERE status = 1 
-- AND end_date IS NOT NULL 
-- AND end_date < NOW()
-- ORDER BY end_date DESC;

-- 4. Показване на предстоящи съобщения
-- SELECT * FROM `{{prefix}}messages` 
-- WHERE status = 1 
-- AND start_date > NOW()
-- ORDER BY start_date ASC;

-- 5. Статистика за съобщенията
-- SELECT 
--     COUNT(*) as total_messages,
--     SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_messages,
--     SUM(CASE WHEN status = 1 AND start_date <= NOW() AND (end_date IS NULL OR end_date >= NOW()) THEN 1 ELSE 0 END) as current_messages,
--     SUM(CASE WHEN status = 1 AND end_date IS NOT NULL AND end_date < NOW() THEN 1 ELSE 0 END) as expired_messages,
--     SUM(CASE WHEN status = 1 AND start_date > NOW() THEN 1 ELSE 0 END) as upcoming_messages
-- FROM `{{prefix}}messages`;

-- Инструкции за използване:
-- 1. Заменете {{prefix}} с действителния префикс на вашата OpenCart база данни
-- 2. Изпълнете скрипта в PHPMyAdmin или друг MySQL клиент
-- 3. Добавете правата за достъп в админ панела ако е необходимо
-- 4. Добавете връзка към модула в админ менюто

-- За добавяне на връзка в админ менюто, добавете следния код в съответния menu файл:
-- $data['menus'][] = array(
--     'id'       => 'menu-design-messages',
--     'icon'     => 'fa-comments-o',
--     'name'     => 'Съобщения',
--     'href'     => $this->url->link('design/messages', 'token=' . $this->session->data['token'], true),
--     'children' => array()
-- );
