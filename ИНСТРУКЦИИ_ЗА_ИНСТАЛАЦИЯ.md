# Модул за управление на съобщения - OpenCart 2.3

## Описание
Този модул позволява управление на съобщения в админ панела на OpenCart 2.3. Съобщенията могат да се показват на различни места в сайта с възможност за задаване на период на показване.

## Файлова структура
```
admin/
├── controller/design/messages.php          # Контролер за админ панела
├── model/design/messages.php               # Модел за работа с базата данни
├── view/template/design/
│   ├── messages_list.tpl                   # Списък със съобщения
│   └── messages_form.tpl                   # Форма за добавяне/редактиране
└── language/bg/design/messages.php         # Български езиков файл

install_messages_module.sql                 # SQL скрипт за инсталация
```

## Стъпки за инсталация

### 1. Копиране на файловете
Копирайте всички файлове в съответните директории на вашата OpenCart инсталация.

### 2. Създаване на базата данни
1. Отворете PHPMyAdmin
2. Изберете базата данни на вашия OpenCart
3. Отворете файла `install_messages_module.sql`
4. Заменете `{{prefix}}` с действителния префикс на вашата база данни (обикновено `oc_`)
5. Изпълнете SQL скрипта

### 3. Добавяне на права за достъп
В админ панела:
1. Отидете в System > Users > User Groups
2. Редактирайте групата "Administrator"
3. В "Access Permission" добавете: `design/messages`
4. В "Modify Permission" добавете: `design/messages`
5. Запазете промените

### 4. Добавяне в админ менюто
За да добавите връзка в админ менюто, редактирайте файла:
`admin/controller/common/column_left.php`

Намерете секцията с Design менюто и добавете:
```php
$design[] = array(
    'name'     => $this->language->get('text_messages'),
    'href'     => $this->url->link('design/messages', 'token=' . $this->session->data['token'], true),
    'children' => array()
);
```

Също така добавете в езиковия файл `admin/language/bg/common/column_left.php`:
```php
$_['text_messages'] = 'Съобщения';
```

## Функционалности

### Основни функции
- ✅ Добавяне на нови съобщения
- ✅ Редактиране на съществуващи съобщения
- ✅ Изтриване на съобщения
- ✅ Bulk операции за промяна на статус
- ✅ Филтриране и сортиране
- ✅ Пагинация

### Полета на съобщенията
- **Наименование** - кратко описание на съобщението
- **Текст** - HTML съдържание с rich text editor
- **Начална дата** - кога да започне показването
- **Крайна дата** - кога да спре показването (опционално)
- **Място за показване** - къде да се показва съобщението
- **Статус** - включено/изключено

### Места за показване
Текущо поддържани места:
- `index_page` - "Начална страница под Hero банера"

За добавяне на нови места, редактирайте метода `getDisplayPlaces()` в контролера и добавете съответните текстове в езиковия файл.

## Използване във frontend

За да покажете съобщенията във frontend, използвайте следния код в контролера:

```php
// Зареждане на модела
$this->load->model('design/messages');

// Получаване на активни съобщения за конкретно място
$messages = $this->model_design_messages->getMessagesByPlace('index_page');

// Предаване към view
$data['messages'] = $messages;
```

В template файла:
```php
<?php if (!empty($messages)) { ?>
<div class="messages-container">
    <?php foreach ($messages as $message) { ?>
    <div class="message-item">
        <h3><?php echo $message['name']; ?></h3>
        <div class="message-content">
            <?php echo $message['text']; ?>
        </div>
    </div>
    <?php } ?>
</div>
<?php } ?>
```

## Валидация

Модулът включва следните валидации:
- Наименованието трябва да бъде между 3 и 255 символа
- Текстът е задължителен
- Началната дата е задължителна и трябва да бъде в правилен формат
- Крайната дата трябва да бъде след началната (ако е зададена)
- Мястото за показване е задължително

## Дизайн

Модулът включва подобрен дизайн с:
- Модерни toggle switch бутони за статус
- Gradient ефекти на бутоните
- Подобрени alert съобщения
- Rich text editor за съдържанието
- Date/time picker за датите
- Bulk операции с dropdown меню

## Поддръжка

За въпроси или проблеми, моля свържете се с разработчика.

## Версия
1.0.0 - Първоначална версия

## Съвместимост
OpenCart 2.3.x
